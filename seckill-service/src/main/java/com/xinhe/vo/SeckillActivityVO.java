package com.xinhe.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 秒杀活动VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class SeckillActivityVO {
    /**
     * 活动ID
     */
    private Long id;

    /**
     * 关联商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 秒杀价
     */
    private BigDecimal seckillPrice;

    /**
     * 秒杀库存
     */
    private Integer stockCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态(0-未开始,1-进行中,2-已结束)
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 无参构造函数
     */
    public SeckillActivityVO() {
    }
    
    /**
     * 全参构造函数
     */
    public SeckillActivityVO(Long id, String productName, Long productId, Date startTime, Date endTime, Integer stockCount, Integer status) {
        this.id = id;
        this.productName = productName;
        this.productId = productId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.stockCount = stockCount;
        this.status = status;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public BigDecimal getSeckillPrice() {
        return seckillPrice;
    }
    
    public void setSeckillPrice(BigDecimal seckillPrice) {
        this.seckillPrice = seckillPrice;
    }
    
    public Integer getStockCount() {
        return stockCount;
    }
    
    public void setStockCount(Integer stockCount) {
        this.stockCount = stockCount;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}