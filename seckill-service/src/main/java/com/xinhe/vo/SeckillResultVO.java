package com.xinhe.vo;

import lombok.Data;

/**
 * 秒杀结果VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class SeckillResultVO {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 消息
     */
    private String message;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动ID
     */
    private Long activityId;
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getActivityId() {
        return activityId;
    }
    
    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
}