package com.xinhe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinhe.entity.SeckillActivity;
import com.xinhe.dto.SeckillResult;

/**
 * 秒杀服务接口
 */
public interface SeckillService extends IService<SeckillActivity> {
    
    /**
     * 参与秒杀活动
     * 
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 是否秒杀成功
     */
    boolean participateSeckill(Long activityId, Long userId);
    
    /**
     * 查询秒杀结果
     * 
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 秒杀结果
     */
    SeckillResult getSeckillResult(Long activityId, Long userId);
    
    /**
     * 更新秒杀活动状态
     * 
     * @param activityId 活动ID
     */
    void updateActivityStatus(Long activityId);
    
    /**
     * 初始化活动库存到Redis
     * 
     * @param activityId 活动ID
     */
    void initStockInRedis(Long activityId);
}