package com.xinhe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.entity.SeckillActivity;
import com.xinhe.mapper.SeckillActivityMapper;
import com.xinhe.mq.service.MQProducerService;
import com.xinhe.dto.SeckillResult;
import com.xinhe.service.SeckillService;
import com.xinhe.exception.SeckillException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 秒杀服务实现类
 */
@Service
@Slf4j
@Transactional
public class SeckillServiceImpl extends ServiceImpl<SeckillActivityMapper, SeckillActivity> implements SeckillService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private MQProducerService mqProducerService;

    // Redis键前缀
    private static final String STOCK_KEY_PREFIX = "seckill:stock:";

    // Redis中用户参与记录的key前缀
    private static final String USER_PARTICIPATION_KEY_PREFIX = "seckill:user:";

    // Redis中活动状态的key前缀
    private static final String ACTIVITY_STATUS_KEY_PREFIX = "seckill:status:";

    /**
     * 参与秒杀活动
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return 是否成功加入秒杀队列
     */
    @Override
    public boolean participateSeckill(Long activityId, Long userId) {
        try {
            // 1. 检查活动状态
            SeckillActivity activity = checkActivityStatus(activityId);

            // 2. 检查用户是否已经参与过该活动
            String userParticipationKey = USER_PARTICIPATION_KEY_PREFIX + activityId + ":" + userId;
            Boolean hasParticipated = (Boolean) redisTemplate.opsForValue().get(userParticipationKey);
            if (Boolean.TRUE.equals(hasParticipated)) {
                log.warn("用户 {} 已经参与过秒杀活动 {}", userId, activityId);
                throw new SeckillException(1001, "您已经参与过该秒杀活动");
            }

            // 3. 检查库存
            String stockKey = STOCK_KEY_PREFIX + activityId;
            Long stock = redisTemplate.boundValueOps(stockKey).increment(-1);
            if (stock < 0) {
                // 库存不足，恢复库存
                redisTemplate.boundValueOps(stockKey).increment(1);
                log.warn("秒杀活动 {} 库存不足", activityId);
                throw new SeckillException(1002, "秒杀活动库存不足");
            }

            // 4. 记录用户参与信息
            redisTemplate.opsForValue().set(userParticipationKey, true, 60, TimeUnit.MINUTES);

            // 5. 发送消息到MQ处理订单创建等后续操作
            Map<String, Object> message = new HashMap<>();
            message.put("activityId", activityId);
            message.put("userId", userId);
            message.put("timestamp", System.currentTimeMillis());
            mqProducerService.sendSeckillMessage(message, "seckill.order.create");

            log.info("用户 {} 成功参与秒杀活动 {}", userId, activityId);
            return true;
        } catch (SeckillException e) {
            log.error("秒杀异常 - 活动ID: {}, 用户ID: {}, 错误码: {}, 错误信息: {}",
                     activityId, userId, e.getCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("秒杀过程中发生未知异常 - 活动ID: {}, 用户ID: {}", activityId, userId, e);
            return false;
        }
    }

    /**
     * 检查活动状态
     *
     * @param activityId 活动ID
     * @return 秒杀活动
     */
    private SeckillActivity checkActivityStatus(Long activityId) {
        // 从Redis获取活动状态
        String statusKey = ACTIVITY_STATUS_KEY_PREFIX + activityId;
        Integer status = (Integer) redisTemplate.opsForValue().get(statusKey);

        if (status == null) {
            // Redis中没有缓存，从数据库查询
            SeckillActivity activity = getById(activityId);
            if (activity == null) {
                log.warn("秒杀活动 {} 不存在", activityId);
                throw new SeckillException(1003, "秒杀活动不存在");
            }

            // 更新Redis缓存
            redisTemplate.opsForValue().set(statusKey, activity.getStatus(), 60, TimeUnit.SECONDS);
            status = activity.getStatus();
        }

        // 检查活动状态
        if (status == 0) {
            log.warn("秒杀活动 {} 尚未开始", activityId);
            throw new SeckillException(1004, "秒杀活动尚未开始");
        } else if (status == 2) {
            log.warn("秒杀活动 {} 已结束", activityId);
            throw new SeckillException(1005, "秒杀活动已结束");
        } else if (status != 1) {
            log.warn("秒杀活动 {} 状态异常: {}", activityId, status);
            throw new SeckillException(1006, "秒杀活动状态异常");
        }

        return getById(activityId);
    }


    /**
     * 获取秒杀结果
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return 秒杀结果
     */
    @Override
    public SeckillResult getSeckillResult(Long activityId, Long userId) {
        // 检查用户是否参与过该活动
        String userParticipationKey = USER_PARTICIPATION_KEY_PREFIX + activityId + ":" + userId;
        Boolean hasParticipated = (Boolean) redisTemplate.opsForValue().get(userParticipationKey);

        SeckillResult result = new SeckillResult();
        if (Boolean.TRUE.equals(hasParticipated)) {
            result.setSuccess(true);
            result.setMessage("秒杀成功");
        } else {
            result.setSuccess(false);
            result.setMessage("未参与秒杀或秒杀未成功");
        }

        return result;
    }

    /**
     * 更新活动状态
     *
     * @param activityId 活动ID
     */
    @Override
    public void updateActivityStatus(Long activityId) {
        SeckillActivity activity = getById(activityId);
        if (activity != null) {
            Date now = new Date();
            if (now.before(activity.getStartTime())) {
                activity.setStatus(0); // 未开始
            } else if (now.after(activity.getEndTime())) {
                activity.setStatus(2); // 已结束
            } else {
                activity.setStatus(1); // 进行中
            }
            updateById(activity);

            // 更新Redis缓存
            String statusKey = ACTIVITY_STATUS_KEY_PREFIX + activityId;
            redisTemplate.opsForValue().set(statusKey, activity.getStatus(), 60, TimeUnit.SECONDS);
        }
    }

    /**
     * 初始化活动库存到Redis
     *
     * @param activityId 活动ID
     */
    public void initStockInRedis(Long activityId) {
        SeckillActivity activity = getById(activityId);
        if (activity != null) {
            String stockKey = STOCK_KEY_PREFIX + activityId;
            redisTemplate.opsForValue().set(stockKey, activity.getStockCount());
        }
    }
}