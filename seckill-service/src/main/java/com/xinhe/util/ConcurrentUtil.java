package com.xinhe.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高并发工具类
 * 提供原子操作和并发安全的数据结构
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Component
public class ConcurrentUtil {

    /**
     * 用户参与秒杀活动计数器
     * key: activityId, value: 参与人数计数器
     */
    private final ConcurrentHashMap<Long, AtomicInteger> userParticipationCount = new ConcurrentHashMap<>();
    
    /**
     * 订单ID生成器
     */
    private final AtomicLong orderIdGenerator = new AtomicLong(System.currentTimeMillis());

    /**
     * 获取或创建用户参与计数器
     *
     * @param activityId 活动ID
     * @return AtomicInteger 计数器
     */
    public AtomicInteger getUserParticipationCounter(Long activityId) {
        return userParticipationCount.computeIfAbsent(activityId, k -> new AtomicInteger(0));
    }

    /**
     * 增加用户参与计数
     *
     * @param activityId 活动ID
     * @return int 当前参与人数
     */
    public int incrementUserParticipation(Long activityId) {
        return getUserParticipationCounter(activityId).incrementAndGet();
    }

    /**
     * 生成订单ID
     *
     * @return long 订单ID
     */
    public long generateOrderId() {
        return orderIdGenerator.incrementAndGet();
    }

    /**
     * 获取当前参与人数
     *
     * @param activityId 活动ID
     * @return int 当前参与人数
     */
    public int getCurrentParticipationCount(Long activityId) {
        AtomicInteger counter = userParticipationCount.get(activityId);
        return counter != null ? counter.get() : 0;
    }
}