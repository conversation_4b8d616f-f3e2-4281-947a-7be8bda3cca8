package com.xinhe.controller;

import com.xinhe.dto.ParticipateSeckillDTO;
import com.xinhe.dto.SeckillActivityDTO;
import com.xinhe.dto.SeckillResult;
import com.xinhe.service.SeckillService;
import com.xinhe.vo.SeckillActivityVO;
import com.xinhe.vo.SeckillResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

/**
 * 秒杀活动控制器
 * 提供秒杀活动的创建、参与、查询结果等接口
 */
@Slf4j
@RestController
@RequestMapping("/api/seckill")
public class SeckillController {
    
    @Autowired
    private SeckillService seckillService;

    /**
     * 创建秒杀活动
     * 
     * @param seckillActivityDTO 秒杀活动信息
     * @return 创建结果
     */
    @PostMapping("/activity")
    public SeckillActivityVO createSeckillActivity(@Valid @RequestBody SeckillActivityDTO seckillActivityDTO) {
        // 这里应该调用service层创建秒杀活动
        // 由于这是一个简化版本，我们直接返回一个模拟的活动VO
        SeckillActivityVO vo = new SeckillActivityVO();
        vo.setId(1L);
        vo.setProductName("测试商品");
        vo.setProductId(seckillActivityDTO.getProductId());
        vo.setStartTime(seckillActivityDTO.getStartTime());
        vo.setEndTime(seckillActivityDTO.getEndTime());
        vo.setStockCount(seckillActivityDTO.getStockCount());
        vo.setStatus(1); // 进行中
        return vo;
    }
    
    /**
     * 获取秒杀活动列表
     * 
     * @return 秒杀活动列表
     */
    @GetMapping("/activities")
    public List<SeckillActivityVO> getSeckillActivities() {
        // 这里应该从数据库查询活动列表
        // 由于这是一个简化版本，我们直接返回一个模拟的活动列表
        List<SeckillActivityVO> list = new ArrayList<>();
        SeckillActivityVO vo1 = new SeckillActivityVO();
        vo1.setId(1L);
        vo1.setProductName("秒杀活动1");
        vo1.setProductId(1L);
        vo1.setStartTime(new Date());
        vo1.setEndTime(new Date(System.currentTimeMillis() + 3600000));
        vo1.setStockCount(100);
        vo1.setStatus(1);
        list.add(vo1);
        
        SeckillActivityVO vo2 = new SeckillActivityVO();
        vo2.setId(2L);
        vo2.setProductName("秒杀活动2");
        vo2.setProductId(2L);
        vo2.setStartTime(new Date());
        vo2.setEndTime(new Date(System.currentTimeMillis() + 7200000));
        vo2.setStockCount(50);
        vo2.setStatus(1);
        list.add(vo2);
        
        return list;
    }
    
    /**
     * 参与秒杀
     * 
     * @param dto 参与秒杀信息
     * @return 秒杀结果
     */
    @PostMapping("/participate")
    public SeckillResultVO participateSeckill(@Valid @RequestBody ParticipateSeckillDTO dto) {
        log.info("用户 {} 参与秒杀活动 {}", dto.getUserId(), dto.getActivityId());
        
        // 参数校验
        if (dto.getActivityId() == null || dto.getActivityId() <= 0) {
            SeckillResultVO resultVO = new SeckillResultVO();
            resultVO.setSuccess(false);
            resultVO.setMessage("活动ID无效");
            return resultVO;
        }
        
        if (dto.getUserId() == null || dto.getUserId() <= 0) {
            SeckillResultVO resultVO = new SeckillResultVO();
            resultVO.setSuccess(false);
            resultVO.setMessage("用户ID无效");
            return resultVO;
        }
        
        // 调用秒杀服务参与秒杀
        boolean success = seckillService.participateSeckill(dto.getActivityId(), dto.getUserId());
        
        SeckillResultVO resultVO = new SeckillResultVO();
        if (success) {
            resultVO.setSuccess(true);
            resultVO.setMessage("秒杀成功，请稍后查询结果");
        } else {
            resultVO.setSuccess(false);
            resultVO.setMessage("秒杀失败，可能库存不足或已参与过该活动");
        }
        
        return resultVO;
    }
    
    /**
     * 获取秒杀结果
     * 
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 秒杀结果
     */
    @GetMapping("/result")
    public SeckillResultVO getSeckillResult(
            @RequestParam @NotNull(message = "活动ID不能为空") Long activityId,
            @RequestParam @NotNull(message = "用户ID不能为空") Long userId) {
        log.info("查询秒杀结果 - 活动ID: {}, 用户ID: {}", activityId, userId);
        
        // 参数校验
        if (activityId <= 0) {
            SeckillResultVO resultVO = new SeckillResultVO();
            resultVO.setSuccess(false);
            resultVO.setMessage("活动ID无效");
            return resultVO;
        }
        
        if (userId <= 0) {
            SeckillResultVO resultVO = new SeckillResultVO();
            resultVO.setSuccess(false);
            resultVO.setMessage("用户ID无效");
            return resultVO;
        }
        
        // 调用秒杀服务获取秒杀结果
        SeckillResult result = seckillService.getSeckillResult(activityId, userId);
        
        SeckillResultVO resultVO = new SeckillResultVO();
        resultVO.setSuccess(result.isSuccess());
        resultVO.setMessage(result.getMessage());
        
        return resultVO;
    }
}