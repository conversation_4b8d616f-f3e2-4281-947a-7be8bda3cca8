package com.xinhe.exception;

/**
 * 秒杀相关异常类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
public class SeckillException extends BaseException {
    public static final int SECKILL_ACTIVITY_NOT_FOUND = 1001;
    public static final int SECKILL_ACTIVITY_NOT_STARTED = 1002;
    public static final int SECKILL_ACTIVITY_ENDED = 1003;
    public static final int SECKILL_STOCK_NOT_ENOUGH = 1004;
    public static final int SECKILL_USER_PARTICIPATED = 1005;
    public static final int SECKILL_PROCESS_FAILED = 1006;

    public SeckillException(int code, String message) {
        super(code, message);
    }

    public SeckillException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 活动未找到异常
     *
     * @return SeckillException
     */
    public static SeckillException activityNotFound() {
        return new SeckillException(SECKILL_ACTIVITY_NOT_FOUND, "秒杀活动不存在");
    }

    /**
     * 活动未开始异常
     *
     * @return SeckillException
     */
    public static SeckillException activityNotStarted() {
        return new SeckillException(SECKILL_ACTIVITY_NOT_STARTED, "秒杀活动尚未开始");
    }

    /**
     * 活动已结束异常
     *
     * @return SeckillException
     */
    public static SeckillException activityEnded() {
        return new SeckillException(SECKILL_ACTIVITY_ENDED, "秒杀活动已结束");
    }

    /**
     * 库存不足异常
     *
     * @return SeckillException
     */
    public static SeckillException stockNotEnough() {
        return new SeckillException(SECKILL_STOCK_NOT_ENOUGH, "秒杀库存不足");
    }

    /**
     * 用户已参与异常
     *
     * @return SeckillException
     */
    public static SeckillException userParticipated() {
        return new SeckillException(SECKILL_USER_PARTICIPATED, "用户已参与该秒杀活动");
    }

    /**
     * 秒杀处理失败异常
     *
     * @return SeckillException
     */
    public static SeckillException processFailed(String message) {
        return new SeckillException(SECKILL_PROCESS_FAILED, message);
    }
}