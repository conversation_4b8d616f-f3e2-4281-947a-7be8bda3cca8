package com.xinhe.exception;

import com.xinhe.vo.SeckillResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 处理控制器层抛出的异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义业务异常
     *
     * @param e 业务异常
     * @return 异常响应结果
     */
    @ExceptionHandler(BaseException.class)
    public Map<String, Object> handleBaseException(BaseException e) {
        log.error("业务异常，错误码: {}, 错误信息: {}", e.getCode(), e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", e.getCode());
        result.put("message", e.getMessage());
        return result;
    }

    /**
     * 处理自定义的秒杀异常
     *
     * @param ex 异常对象
     * @return 错误结果
     */
    @ExceptionHandler(SeckillException.class)
    public SeckillResultVO handleSeckillException(SeckillException ex) {
        log.error("处理秒杀异常: {}", ex.getMessage(), ex);
        SeckillResultVO resultVO = new SeckillResultVO();
        resultVO.setSuccess(false);
        resultVO.setMessage(ex.getMessage());
        return resultVO;
    }

    /**
     * 处理其他未捕获的异常
     *
     * @param ex 异常对象
     * @return 错误结果
     */
    @ExceptionHandler(Exception.class)
    public SeckillResultVO handleGeneralException(Exception ex) {
        log.error("处理未知异常: {}", ex.getMessage(), ex);
        SeckillResultVO resultVO = new SeckillResultVO();
        resultVO.setSuccess(false);
        resultVO.setMessage("系统内部错误: " + ex.getMessage());
        return resultVO;
    }
}