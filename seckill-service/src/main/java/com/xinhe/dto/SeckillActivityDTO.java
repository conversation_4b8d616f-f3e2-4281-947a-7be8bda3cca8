package com.xinhe.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 秒杀活动DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class SeckillActivityDTO {
    /**
     * 活动ID
     */
    private Long id;

    /**
     * 关联商品ID
     */
    private Long productId;

    /**
     * 秒杀价
     */
    private BigDecimal seckillPrice;

    /**
     * 秒杀库存
     */
    private Integer stockCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态(0-未开始,1-进行中,2-已结束)
     */
    private Integer status;
    
    public Long getId() {
        return id;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public BigDecimal getSeckillPrice() {
        return seckillPrice;
    }
    
    public Integer getStockCount() {
        return stockCount;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public Integer getStatus() {
        return status;
    }
}