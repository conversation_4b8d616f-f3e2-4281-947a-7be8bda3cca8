-- 初始化测试数据脚本

-- 用户数据
INSERT INTO t_user (username, password, phone, email, status) VALUES 
('testuser', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138000', '<EMAIL>', 1),
('user1', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138001', '<EMAIL>', 1),
('user2', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138002', '<EMAIL>', 1),
('user3', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138003', '<EMAIL>', 1),
('user4', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138004', '<EMAIL>', 1),
('user5', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138005', '<EMAIL>', 1),
('user6', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138006', '<EMAIL>', 1),
('user7', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138007', '<EMAIL>', 1),
('user8', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138008', '<EMAIL>', 1),
('user9', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138009', '<EMAIL>', 1),
('user10', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138010', '<EMAIL>', 1),
('user11', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138011', '<EMAIL>', 1),
('user12', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138012', '<EMAIL>', 1),
('user13', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138013', '<EMAIL>', 1),
('user14', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138014', '<EMAIL>', 1),
('user15', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138015', '<EMAIL>', 1),
('user16', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138016', '<EMAIL>', 1),
('user17', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138017', '<EMAIL>', 1),
('user18', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138018', '<EMAIL>', 1),
('user19', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138019', '<EMAIL>', 1),
('user20', '$2a$10$GcLQxlXUqT5X7k4NQ8gJOO3LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W9LdL7ZJ7W', '13800138020', '<EMAIL>', 1);

-- 商品数据
INSERT INTO t_product (name, description, price, stock, image_url, category_id) VALUES 
('iPhone 15 Pro', 'Apple最新款手机，配备A17 Pro芯片', 999.99, 500, 'https://example.com/iphone15pro.jpg', 1),
('Samsung Galaxy S24', '三星旗舰手机，配备最新处理器', 899.99, 300, 'https://example.com/s24.jpg', 1),
('MacBook Pro 16', '苹果笔记本电脑，M3 Pro芯片', 2499.99, 100, 'https://example.com/macbookpro16.jpg', 2),
('Dell XPS 13', '戴尔超极本，轻薄便携', 1299.99, 150, 'https://example.com/xps13.jpg', 2),
('iPad Air', '苹果平板电脑，支持Apple Pencil', 599.99, 200, 'https://example.com/ipadair.jpg', 3),
('Sony WH-1000XM5', '索尼降噪耳机，音质出色', 399.99, 150, 'https://example.com/sonyheadphones.jpg', 4),
('Apple Watch Series 9', '苹果智能手表，健康监测', 499.99, 200, 'https://example.com/applewatch.jpg', 5),
('Nintendo Switch OLED', '任天堂游戏机，OLED屏幕', 349.99, 100, 'https://example.com/nintendoswitch.jpg', 6),
('PlayStation 5', '索尼游戏主机，次世代游戏体验', 499.99, 80, 'https://example.com/ps5.jpg', 6),
('Xbox Series X', '微软游戏主机，4K游戏体验', 449.99, 90, 'https://example.com/xboxseriesx.jpg', 6),
('Canon EOS R5', '佳能全画幅无反相机', 3899.99, 30, 'https://example.com/canonr5.jpg', 7),
('DJI Mavic 3', '大疆无人机，三摄航拍', 2199.99, 50, 'https://example.com/djimavic3.jpg', 8),
('Tesla Model 3', '特斯拉电动汽车', 29999.99, 5, 'https://example.com/teslamodel3.jpg', 9),
('Nike Air Jordan 1', '耐克篮球鞋经典款', 169.99, 300, 'https://example.com/airjordan1.jpg', 10),
('Rolex Submariner', '劳力士潜水表', 9999.99, 10, 'https://example.com/rolex.jpg', 11),
('AirPods Pro 2', '苹果无线降噪耳机', 249.99, 200, 'https://example.com/airpodspro2.jpg', 4),
('Surface Pro 9', '微软平板电脑', 1199.99, 80, 'https://example.com/surfacepro9.jpg', 3),
('GoPro Hero 12', '运动相机', 399.99, 60, 'https://example.com/goprohero12.jpg', 7),
('Bose QuietComfort 45', 'BOSE降噪耳机', 329.99, 100, 'https://example.com/boseqc45.jpg', 4),
('Fitbit Versa 4', '健康追踪智能手表', 199.99, 120, 'https://example.com/fitbitversa4.jpg', 5);

-- 秒杀活动数据
INSERT INTO t_seckill_activity (product_id, seckill_price, stock_count, start_time, end_time, status) VALUES 
(1, 899.99, 50, '2025-09-05 10:00:00', '2025-09-05 12:00:00', 0),
(2, 799.99, 30, '2025-09-05 14:00:00', '2025-09-05 16:00:00', 0),
(3, 2299.99, 20, '2025-09-06 10:00:00', '2025-09-06 12:00:00', 0),
(4, 1199.99, 25, '2025-09-06 14:00:00', '2025-09-06 16:00:00', 0),
(5, 499.99, 40, '2025-09-07 10:00:00', '2025-09-07 12:00:00', 0),
(6, 349.99, 30, '2025-09-07 14:00:00', '2025-09-07 16:00:00', 0),
(7, 449.99, 35, '2025-09-08 10:00:00', '2025-09-08 12:00:00', 0),
(8, 299.99, 20, '2025-09-08 14:00:00', '2025-09-08 16:00:00', 0),
(9, 3999.99, 5, '2025-09-09 10:00:00', '2025-09-09 12:00:00', 0),
(10, 1999.99, 8, '2025-09-09 14:00:00', '2025-09-09 16:00:00', 0),
(11, 149.99, 40, '2025-09-10 10:00:00', '2025-09-10 12:00:00', 0),
(12, 1099.99, 15, '2025-09-10 14:00:00', '2025-09-10 16:00:00', 0),
(13, 299.99, 25, '2025-09-11 10:00:00', '2025-09-11 12:00:00', 0),
(14, 249.99, 30, '2025-09-11 14:00:00', '2025-09-11 16:00:00', 0),
(15, 179.99, 35, '2025-09-12 10:00:00', '2025-09-12 12:00:00', 0),
(16, 29999.99, 1, '2025-09-12 14:00:00', '2025-09-12 16:00:00', 0),
(17, 9999.99, 2, '2025-09-13 10:00:00', '2025-09-13 12:00:00', 0),
(18, 169.99, 50, '2025-09-13 14:00:00', '2025-09-13 16:00:00', 0),
(19, 399.99, 40, '2025-09-14 10:00:00', '2025-09-14 12:00:00', 0),
(20, 349.99, 30, '2025-09-14 14:00:00', '2025-09-14 16:00:00', 0);