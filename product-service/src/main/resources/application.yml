server:
  port: 8002

spring:
  application:
    name: product-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************
    username: root
    password: root
  redis:
    host: ***************
    port: 6379
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
    timeout: 3000
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

seata:
  enabled: true
  application-id: product-service
  tx-service-group: default_tx_group
  enable-auto-data-source-proxy: true
  service:
    vgroup-mapping:
      default_tx_group: default
    grouplist:
      default: localhost:8091
  config:
    type: file

# 跨域配置
spring.mvc.cors.allowedOriginPatterns: "*"
spring.mvc.cors.allowedMethods: "*"
spring.mvc.cors.allowedHeaders: "*"
spring.mvc.cors.allowCredentials: true