package com.xinhe.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinhe.entity.Product;
import com.xinhe.entity.SeckillActivity;

import java.util.List;

/**
 * 商品服务接口
 * 
 * <AUTHOR> Name
 */
public interface ProductService extends IService<Product> {
    
    /**
     * 分页查询商品列表
     * 
     * @param page 分页对象
     * @param keyword 搜索关键词
     * @param categoryId 分类ID
     * @return 商品分页数据
     */
    IPage<Product> getProductList(Page<Product> page, String keyword, Long categoryId);
    
    /**
     * 根据商品ID获取商品详情
     * 
     * @param id 商品ID
     * @return 商品详情
     */
    Product getProductDetail(Long id);
    
    /**
     * 根据商品ID获取秒杀活动
     * 
     * @param productId 商品ID
     * @return 秒杀活动信息
     */
    SeckillActivity getSeckillActivityByProductId(Long productId);
    
    /**
     * 根据活动ID获取秒杀活动详情
     * 
     * @param activityId 活动ID
     * @return 秒杀活动详情
     */
    SeckillActivity getSeckillActivityDetail(Long activityId);
}