package com.xinhe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.entity.Product;
import com.xinhe.entity.SeckillActivity;
import com.xinhe.mapper.ProductMapper;
import com.xinhe.mapper.SeckillActivityMapper;
import com.xinhe.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品服务实现类
 * 
 * <AUTHOR> Name
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private SeckillActivityMapper seckillActivityMapper;
    
    @Override
    public IPage<Product> getProductList(Page<Product> page, String keyword, Long categoryId) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        
        // 添加搜索条件
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like("name", keyword);
        }
        
        // 添加分类条件
        if (categoryId != null && categoryId > 0) {
            queryWrapper.eq("category_id", categoryId);
        }
        
        // 只查询上架商品
        queryWrapper.eq("status", 1);
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");
        
        return productMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    public Product getProductDetail(Long id) {
        return productMapper.selectById(id);
    }
    
    @Override
    public SeckillActivity getSeckillActivityByProductId(Long productId) {
        QueryWrapper<SeckillActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", productId);
        return seckillActivityMapper.selectOne(queryWrapper);
    }
    
    @Override
    public SeckillActivity getSeckillActivityDetail(Long activityId) {
        return seckillActivityMapper.selectById(activityId);
    }
}