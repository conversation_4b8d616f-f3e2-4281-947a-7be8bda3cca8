package com.xinhe.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class ProductVO {
    /**
     * 商品ID
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 原价
     */
    private BigDecimal price;

    /**
     * 普通库存
     */
    private Integer stock;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 状态(0-下架,1-上架)
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}