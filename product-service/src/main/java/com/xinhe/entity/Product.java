package com.xinhe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品实体类
 * 
 * <AUTHOR> Name
 */
@Data
@TableName("t_product")
public class Product {
    /**
     * 商品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 商品名称
     */
    private String name;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 原价
     */
    private BigDecimal price;
    
    /**
     * 普通库存
     */
    private Integer stock;
    
    /**
     * 商品图片URL
     */
    private String imageUrl;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 状态(0-下架,1-上架)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 秒杀活动信息
     */
    private SeckillActivity seckillActivity;
    
    /**
     * 设置秒杀活动
     * 
     * @param seckillActivity 秒杀活动
     */
    public void setSeckillActivity(SeckillActivity seckillActivity) {
        this.seckillActivity = seckillActivity;
    }
}