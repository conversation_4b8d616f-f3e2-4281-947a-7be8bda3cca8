package com.xinhe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 秒杀活动实体类
 * 
 * <AUTHOR> Name
 */
@Data
@TableName("t_seckill_activity")
public class SeckillActivity {
    /**
     * 活动ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联商品ID
     */
    private Long productId;
    
    /**
     * 秒杀价
     */
    private BigDecimal seckillPrice;
    
    /**
     * 秒杀库存
     */
    private Integer stockCount;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 状态(0-未开始,1-进行中,2-已结束)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 获取开始时间
     * 
     * @return 开始时间
     */
    public Date getStartTime() {
        return startTime;
    }
    
    /**
     * 获取结束时间
     * 
     * @return 结束时间
     */
    public Date getEndTime() {
        return endTime;
    }
    
    /**
     * 设置状态
     * 
     * @param status 状态
     */
    public void setStatus(int status) {
        this.status = status;
    }
}