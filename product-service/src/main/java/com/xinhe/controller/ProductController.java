package com.xinhe.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinhe.entity.Product;
import com.xinhe.entity.SeckillActivity;
import com.xinhe.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品控制器
 * 
 * <AUTHOR> Name
 */
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    /**
     * 获取商品列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param categoryId 分类ID
     * @return 商品列表
     */
    @GetMapping
    public Map<String, Object> getProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<Product> pageObj = new Page<>(page, size);
            IPage<Product> productIPage = productService.getProductList(pageObj, keyword, categoryId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("list", productIPage.getRecords());
            data.put("total", productIPage.getTotal());
            data.put("page", productIPage.getCurrent());
            data.put("size", productIPage.getSize());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取商品列表失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取商品详情
     * 
     * @param id 商品ID
     * @return 商品详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getProductDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Product product = productService.getProductDetail(id);
            if (product != null) {
                // 获取秒杀活动信息
                SeckillActivity seckillActivity = productService.getSeckillActivityByProductId(id);
                product.setSeckillActivity(seckillActivity);
                
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", product);
            } else {
                result.put("code", 404);
                result.put("message", "商品不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取商品详情失败: " + e.getMessage());
        }
        
        return result;
    }
}