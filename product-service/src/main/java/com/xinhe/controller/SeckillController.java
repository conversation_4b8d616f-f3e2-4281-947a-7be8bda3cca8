package com.xinhe.controller;

import com.xinhe.entity.SeckillActivity;
import com.xinhe.service.ProductService;
import com.xinhe.service.SeckillService;
import com.xinhe.service.SeckillResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 秒杀控制器
 * 
 * <AUTHOR> Name
 */
@RestController
@RequestMapping("/api/seckill")
public class SeckillController {
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private SeckillService seckillService;
    
    /**
     * 获取秒杀活动详情
     * 
     * @param activityId 活动ID
     * @return 秒杀活动详情
     */
    @GetMapping("/activities/{activityId}")
    public Map<String, Object> getSeckillActivity(@PathVariable Long activityId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SeckillActivity activity = productService.getSeckillActivityDetail(activityId);
            if (activity != null) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", activity);
            } else {
                result.put("code", 404);
                result.put("message", "秒杀活动不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取秒杀活动详情失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 参与秒杀
     * 
     * @param activityId 活动ID
     * @param userId 用户ID (从请求头或token中获取)
     * @return 秒杀结果
     */
    @PostMapping("/{activityId}")
    public Map<String, Object> participateSeckill(@PathVariable Long activityId, 
                                                  @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            boolean success = seckillService.participateSeckill(activityId, userId);
            SeckillResult seckillResult = seckillService.getSeckillResult(activityId, userId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("success", seckillResult.isSuccess());
            data.put("orderId", seckillResult.getOrderId());
            data.put("message", seckillResult.getMessage());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "参与秒杀失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 查询秒杀结果
     * 
     * @param activityId 活动ID
     * @param userId 用户ID (从请求头或token中获取)
     * @return 秒杀结果
     */
    @GetMapping("/result/{activityId}")
    public Map<String, Object> getSeckillResult(@PathVariable Long activityId,
                                                @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            SeckillResult seckillResult = seckillService.getSeckillResult(activityId, userId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("success", seckillResult.isSuccess());
            data.put("orderId", seckillResult.getOrderId());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询秒杀结果失败: " + e.getMessage());
        }
        
        return result;
    }
}