package com.xinhe.controller;

import com.xinhe.constants.CommonConstants;
import com.xinhe.dto.Result;
import com.xinhe.entity.SeckillActivity;
import com.xinhe.service.ProductService;
import com.xinhe.service.SeckillService;
import com.xinhe.dto.SeckillResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 秒杀控制器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/seckill")
public class SeckillController {
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private SeckillService seckillService;
    
    /**
     * 获取秒杀活动详情
     *
     * @param activityId 活动ID
     * @return 秒杀活动详情
     */
    @GetMapping("/activities/{activityId}")
    public Result<SeckillActivity> getSeckillActivity(@PathVariable Long activityId) {
        try {
            log.info("获取秒杀活动详情，活动ID: {}", activityId);
            SeckillActivity activity = productService.getSeckillActivityDetail(activityId);
            if (activity != null) {
                return Result.success(activity);
            } else {
                return Result.error(CommonConstants.NOT_FOUND_CODE, "秒杀活动不存在");
            }
        } catch (Exception e) {
            log.error("获取秒杀活动详情失败，活动ID: {}", activityId, e);
            return Result.error("获取秒杀活动详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 参与秒杀
     * 
     * @param activityId 活动ID
     * @param userId 用户ID (从请求头或token中获取)
     * @return 秒杀结果
     */
    @PostMapping("/{activityId}")
    public Map<String, Object> participateSeckill(@PathVariable Long activityId, 
                                                  @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            boolean success = seckillService.participateSeckill(activityId, userId);
            SeckillResult seckillResult = seckillService.getSeckillResult(activityId, userId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("success", seckillResult.isSuccess());
            data.put("orderId", seckillResult.getOrderId());
            data.put("message", seckillResult.getMessage());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "参与秒杀失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 查询秒杀结果
     * 
     * @param activityId 活动ID
     * @param userId 用户ID (从请求头或token中获取)
     * @return 秒杀结果
     */
    @GetMapping("/result/{activityId}")
    public Map<String, Object> getSeckillResult(@PathVariable Long activityId,
                                                @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            SeckillResult seckillResult = seckillService.getSeckillResult(activityId, userId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("success", seckillResult.isSuccess());
            data.put("orderId", seckillResult.getOrderId());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询秒杀结果失败: " + e.getMessage());
        }
        
        return result;
    }
}
