package com.xinhe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinhe.entity.SeckillOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 秒杀订单Mapper接口
 * 
 * <AUTHOR> Name
 */
public interface SeckillOrderMapper extends BaseMapper<SeckillOrder> {
    
    /**
     * 根据用户ID和活动ID查询秒杀订单
     * 
     * @param userId 用户ID
     * @param activityId 活动ID
     * @return 秒杀订单
     */
    @Select("SELECT * FROM t_seckill_order WHERE user_id = #{userId} AND activity_id = #{activityId}")
    SeckillOrder findByUserIdAndActivityId(@Param("userId") Long userId, @Param("activityId") Long activityId);
}