package com.xinhe.exception;

/**
 * 自定义异常基类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
public class BaseException extends RuntimeException {
    private int code;
    private String message;

    public BaseException(String message) {
        super(message);
        this.message = message;
    }

    public BaseException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BaseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}