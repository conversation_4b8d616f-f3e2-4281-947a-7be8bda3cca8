package com.xinhe.exception;

/**
 * 商品相关异常类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
public class ProductException extends BaseException {
    public static final int PRODUCT_NOT_FOUND = 5001;
    public static final int PRODUCT_QUERY_FAILED = 5002;
    public static final int SECKILL_ACTIVITY_NOT_FOUND = 5003;

    public ProductException(int code, String message) {
        super(code, message);
    }

    public ProductException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 商品未找到异常
     *
     * @return ProductException
     */
    public static ProductException productNotFound() {
        return new ProductException(PRODUCT_NOT_FOUND, "商品不存在");
    }

    /**
     * 商品查询失败异常
     *
     * @return ProductException
     */
    public static ProductException productQueryFailed(String message) {
        return new ProductException(PRODUCT_QUERY_FAILED, "商品查询失败: " + message);
    }

    /**
     * 秒杀活动未找到异常
     *
     * @return ProductException
     */
    public static ProductException seckillActivityNotFound() {
        return new ProductException(SECKILL_ACTIVITY_NOT_FOUND, "秒杀活动不存在");
    }
}