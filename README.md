# 秒杀系统前端

这是一个基于Spring Cloud Alibaba的高并发秒杀系统，用于处理大量用户同时抢购限量商品的场景。

## 项目概述

秒杀系统是一个高并发、高性能的分布式系统，用于处理大量用户同时抢购限量商品的场景。该系统基于Spring Cloud Alibaba微服务架构，通过多种技术手段保障系统在高并发场景下的稳定性和可靠性。

## 技术架构

### 整体架构
- 微服务架构：基于Spring Cloud Alibaba
- 注册中心：Nacos
- 配置中心：Nacos
- 网关：Spring Cloud Gateway
- 服务间调用：OpenFeign
- 负载均衡：Ribbon
- 熔断降级：Sentinel
- 分布式事务：Seata
- 缓存：Redis
- 消息队列：RabbitMQ
- 数据库：MySQL

### 服务模块
1. **网关服务(gateway-service)**：统一入口，负责请求路由、鉴权等
2. **用户服务(user-service)**：用户注册、登录、权限管理
3. **商品服务(product-service)**：商品信息管理、库存查询
4. **秒杀服务(seckill-service)**：处理秒杀活动逻辑、库存扣减
5. **订单服务(order-service)**：订单创建、状态管理
6. **支付服务(payment-service)**：支付流程处理、支付状态同步
7. **公共MQ模块(common-mq)**：统一处理消息队列相关功能

## 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本
- MySQL 5.7 或更高版本
- Redis 3.0 或更高版本
- RabbitMQ 3.8 或更高版本

## 数据库初始化

1. 创建数据库：
   ```sql
   CREATE DATABASE seckill_user DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE seckill_product DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE seckill_order DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE seckill_payment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. 执行数据库脚本：
   - `database-schema.sql`：创建表结构
   - `init-data.sql`：初始化基础数据

## 项目构建

### 清理项目
```bash
mvn clean
```

### 编译项目
```bash
mvn compile
```

### 打包项目
```bash
mvn package
```

### 完整构建
```bash
mvn clean compile package
```

## 服务运行

### 单独启动服务
进入各个服务目录，执行：
```bash
mvn spring-boot:run
```

### 使用IDE启动
在IDE中直接运行各个服务的主类：
- `gateway-service`: `com.xinhe.GatewayServiceApplication`
- `user-service`: `com.xinhe.UserServiceApplication`
- `product-service`: `com.xinhe.ProductServiceApplication`
- `order-service`: `com.xinhe.OrderServiceApplication`
- `payment-service`: `com.xinhe.PaymentServiceApplication`
- `seckill-service`: `com.xinhe.SeckillServiceApplication`

启动顺序建议：
1. Nacos注册配置中心
2. Redis
3. RabbitMQ
4. MySQL
5. 网关服务
6. 其他业务服务

## 项目文档

- [技术文档](TECHNICAL_DOCUMENTATION.md)：包含详细的技术实现说明

## 注意事项

1. 确保所有依赖服务（MySQL、Redis、RabbitMQ、Nacos）已正确安装并运行
2. 根据实际环境修改各服务的配置文件
3. 在生产环境中，需要调整线程池、数据库连接池等参数以适应实际负载
4. 建议使用Docker容器化部署以提高部署效率和环境一致性

## 常见问题

### 构建失败
如果在构建过程中遇到问题，请尝试以下解决方案：

1. 确保已安装Java 8或更高版本
2. 确保已安装Maven 3.6或更高版本
3. 清理Maven本地仓库中的相关依赖后重新构建
4. 使用`mvn clean compile -U`强制更新依赖

### 依赖问题
如果遇到依赖问题，请检查：
1. pom.xml文件中的依赖版本是否正确
2. 父pom.xml中的dependencyManagement配置是否完整
3. Maven仓库是否可以正常访问

### 编译错误
如果遇到编译错误，请检查：
1. Java源代码语法是否正确
2. 包名和类名是否匹配文件路径
3. 导入的类是否存在且可访问
4. 是否缺少必要的依赖
