package com.xinhe.filter;

import org.springframework.util.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * 自定义 认证过滤器
 * @Class GlobalFilter 全局过滤器、
 * @Class AuthenticationFilter 自定义认证过滤器
 * @Class InitializingBean 实现该接口，在Bean的所有属性被设置完成后，调用该方法
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthenticationFilter implements GlobalFilter, Ordered, InitializingBean {
    
    private static final Set<String> skipUris = new LinkedHashSet<>();
    
    private final WebClient webClient;
    
    public AuthenticationFilter(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 过滤器方法 检验token是否有效
     * @param exchange 交换机对象
     * @param chain 过滤器链
     * @return Mono<Void> 异步返回值
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = exchange.getRequest().getURI().getPath();
        
        // 处理跨域预检请求(OPTIONS)
        if (exchange.getRequest().getMethod() == HttpMethod.OPTIONS) {
            ServerHttpResponse response = exchange.getResponse();
            response.getHeaders().add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
            response.getHeaders().add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "*");
            response.getHeaders().add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
            response.getHeaders().add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
            response.setStatusCode(HttpStatus.OK);
            return response.setComplete();
        }
        
        // 判断是否跳过
        if (shouldSkip(path)) {
            log.info("跳过的URI： {}", path);
            return chain.filter(exchange);
        }
        
        // 校验token是否有效
        String authorization = exchange.getRequest().getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isEmpty(authorization)){
            log.error("请求未携带token，URI：{}", path);
            return this.writeErrorResponse(exchange, HttpStatus.UNAUTHORIZED, "未携带token");
        }

        // 验证token有效性
        return validateToken(exchange, chain, authorization);
    }

    /**
     * 验证token有效性
     * @param exchange 交换对象
     * @param chain 过滤器链
     * @param token token
     * @return Mono<Void>
     */
    private Mono<Void> validateToken(ServerWebExchange exchange, GatewayFilterChain chain, String token) {
        String path = exchange.getRequest().getURI().getPath();
        
        // 调用user-service验证token
        return webClient.get()
                .uri("http://user-service/oauth/validate?token=" + token)
                .retrieve()
                .bodyToMono(Map.class)
                .flatMap(response -> {
                    Boolean valid = (Boolean) response.get("valid");
                    if (valid != null && valid) {
                        log.info("Token验证通过，URI：{}", path);
                        // 将用户信息添加到请求头中传递给下游服务
                        String username = (String) response.get("username");
                        ServerWebExchange newExchange = exchange.mutate()
                                .request(exchange.getRequest().mutate()
                                        .header("X-User-Name", username)
                                        .build())
                                .build();
                        return chain.filter(newExchange);
                    } else {
                        log.error("Token验证失败，URI：{}", path);
                        return this.writeErrorResponse(exchange, HttpStatus.UNAUTHORIZED, "Token无效");
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("Token验证异常，URI：{}", path, throwable);
                    return this.writeErrorResponse(exchange, HttpStatus.INTERNAL_SERVER_ERROR, "验证token时发生错误: " + throwable.getMessage());
                });
    }

    /**
     * 写入错误响应
     * @param exchange 交换对象
     * @param status HTTP状态码
     * @param message 错误信息
     * @return Mono<Void>
     */
    private Mono<Void> writeErrorResponse(ServerWebExchange exchange, HttpStatus status, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", status.value());
        result.put("message", message);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(jsonString.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("序列化错误响应失败", e);
            return response.setComplete();
        }
    }

    /**
     * 初始化方法 实现InitializingBean接口，在Bean的所有属性被设置完成后，调用该方法
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        skipUris.add("/login");
        skipUris.add("/oauth/token");
        skipUris.add("/oauth/register");
        skipUris.add("/actuator/health");
        skipUris.add("/api/user/register");
        skipUris.add("/api/user/login");
    }
    
    // 跳过登陆方法
    public static boolean shouldSkip(String uri) {
        return skipUris.contains(uri);
    }
    
    /**
     * 设置过滤器优先级，数值越小优先级越高
     * @return 优先级
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}