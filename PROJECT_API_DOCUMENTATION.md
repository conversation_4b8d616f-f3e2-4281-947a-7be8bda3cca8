# 秒杀系统API接口文档

## 1. 概述

本文档详细描述了秒杀系统各服务模块提供的RESTful API接口，包括请求方式、请求参数、响应格式等信息。

## 2. 公共说明

### 2.1 响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 2.2 错误响应格式
```json
{
  "success": false,
  "code": 500,
  "message": "系统异常",
  "data": null
}
```

### 2.3 认证方式
大部分接口需要在请求头中添加Authorization字段：
```
Authorization: Bearer {token}
```

## 3. 网关服务

网关服务作为统一入口，将请求路由到相应的业务服务。

## 4. 用户服务

### 4.1 用户注册
- **URL**: `/api/user/register`
- **Method**: POST
- **认证**: 否

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| phone | string | 否 | 手机号 |
| email | string | 否 | 邮箱 |

#### 请求示例
```json
{
  "username": "testuser",
  "password": "123456",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": 1,
    "createTime": "2023-01-01 12:00:00"
  }
}
```

### 4.2 用户登录
- **URL**: `/oauth/token`
- **Method**: POST
- **认证**: 否

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

#### 请求示例
```json
{
  "username": "testuser",
  "password": "123456"
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzUxMiJ9.xxxxx",
    "tokenType": "Bearer",
    "userId": 1,
    "username": "testuser"
  }
}
```

### 4.3 获取用户信息
- **URL**: `/api/user/profile`
- **Method**: GET
- **认证**: 是

#### 请求参数
无

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": 1,
    "createTime": "2023-01-01 12:00:00"
  }
}
```

## 5. 商品服务

### 5.1 获取商品列表
- **URL**: `/api/product/list`
- **Method**: GET
- **认证**: 否

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "description": "Apple最新款手机，配备A17 Pro芯片",
        "price": 999.99,
        "stock": 500,
        "imageUrl": "https://example.com/iphone15pro.jpg",
        "categoryId": 1,
        "status": 1
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 5.2 获取商品详情
- **URL**: `/api/product/{id}`
- **Method**: GET
- **认证**: 否

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 商品ID |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "iPhone 15 Pro",
    "description": "Apple最新款手机，配备A17 Pro芯片",
    "price": 999.99,
    "stock": 500,
    "imageUrl": "https://example.com/iphone15pro.jpg",
    "categoryId": 1,
    "status": 1
  }
}
```

## 6. 秒杀服务

### 6.1 创建秒杀活动
- **URL**: `/api/seckill/activity`
- **Method**: POST
- **认证**: 是(管理员)

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | long | 是 | 商品ID |
| seckillPrice | decimal | 是 | 秒杀价格 |
| stockCount | int | 是 | 秒杀库存 |
| startTime | datetime | 是 | 开始时间 |
| endTime | datetime | 是 | 结束时间 |

#### 请求示例
```json
{
  "productId": 1,
  "seckillPrice": 899.99,
  "stockCount": 50,
  "startTime": "2023-01-01 10:00:00",
  "endTime": "2023-01-01 12:00:00"
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "productName": "iPhone 15 Pro",
    "productId": 1,
    "seckillPrice": 899.99,
    "stockCount": 50,
    "startTime": "2023-01-01 10:00:00",
    "endTime": "2023-01-01 12:00:00",
    "status": 0
  }
}
```

### 6.2 获取秒杀活动列表
- **URL**: `/api/seckill/activities`
- **Method**: GET
- **认证**: 否

#### 请求参数
无

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "productName": "iPhone 15 Pro",
      "productId": 1,
      "seckillPrice": 899.99,
      "stockCount": 50,
      "startTime": "2023-01-01 10:00:00",
      "endTime": "2023-01-01 12:00:00",
      "status": 1
    }
  ]
}
```

### 6.3 参与秒杀
- **URL**: `/api/seckill/participate`
- **Method**: POST
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| activityId | long | 是 | 活动ID |
| userId | long | 是 | 用户ID |

#### 请求示例
```json
{
  "activityId": 1,
  "userId": 1
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "秒杀成功，请稍后查询结果",
  "data": null
}
```

### 6.4 查询秒杀结果
- **URL**: `/api/seckill/result`
- **Method**: GET
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| activityId | long | 是 | 活动ID |
| userId | long | 是 | 用户ID |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "秒杀成功",
  "data": null
}
```

## 7. 订单服务

### 7.1 创建订单
- **URL**: `/api/order/create`
- **Method**: POST
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | long | 是 | 用户ID |
| productId | long | 是 | 商品ID |
| quantity | int | 是 | 购买数量 |
| seckillActivityId | long | 否 | 秒杀活动ID |

#### 请求示例
```json
{
  "userId": 1,
  "productId": 1,
  "quantity": 1,
  "seckillActivityId": 1
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "20230101120000001",
    "userId": 1,
    "productId": 1,
    "productName": "iPhone 15 Pro",
    "price": 999.99,
    "quantity": 1,
    "totalAmount": 999.99,
    "status": 0
  }
}
```

### 7.2 获取订单列表
- **URL**: `/api/order/list`
- **Method**: GET
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | long | 是 | 用户ID |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "orderNo": "20230101120000001",
        "userId": 1,
        "productId": 1,
        "productName": "iPhone 15 Pro",
        "price": 999.99,
        "quantity": 1,
        "totalAmount": 999.99,
        "status": 1
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 7.3 获取订单详情
- **URL**: `/api/order/{id}`
- **Method**: GET
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 订单ID |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "20230101120000001",
    "userId": 1,
    "productId": 1,
    "productName": "iPhone 15 Pro",
    "price": 999.99,
    "quantity": 1,
    "totalAmount": 999.99,
    "status": 1
  }
}
```

## 8. 支付服务

### 8.1 创建支付
- **URL**: `/api/payment/create`
- **Method**: POST
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | long | 是 | 订单ID |
| userId | long | 是 | 用户ID |
| amount | decimal | 是 | 支付金额 |
| paymentMethod | int | 是 | 支付方式(1-支付宝,2-微信,3-银行卡) |

#### 请求示例
```json
{
  "orderId": 1,
  "userId": 1,
  "amount": 999.99,
  "paymentMethod": 1
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderId": 1,
    "userId": 1,
    "amount": 999.99,
    "paymentMethod": 1,
    "status": 0,
    "paymentNo": "PAY20230101120000001"
  }
}
```

### 8.2 支付通知
- **URL**: `/api/payment/notify`
- **Method**: POST
- **认证**: 是(内部服务)

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| paymentNo | string | 是 | 支付流水号 |
| status | int | 是 | 支付状态(1-支付成功,2-支付失败) |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 8.3 查询支付状态
- **URL**: `/api/payment/status/{paymentNo}`
- **Method**: GET
- **认证**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| paymentNo | string | 是 | 支付流水号 |

#### 响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderId": 1,
    "userId": 1,
    "amount": 999.99,
    "paymentMethod": 1,
    "status": 1,
    "paymentNo": "PAY20230101120000001"
  }
}
```

## 9. 错误码说明

### 9.1 通用错误码
| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 系统内部错误 |

### 9.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 用户已参与过该秒杀活动 |
| 1002 | 秒杀活动库存不足 |
| 1003 | 秒杀活动不存在 |
| 1004 | 秒杀活动尚未开始 |
| 1005 | 秒杀活动已结束 |
| 1006 | 秒杀活动状态异常 |