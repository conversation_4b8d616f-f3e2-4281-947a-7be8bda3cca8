# 秒杀系统项目技术文档

## 1. 项目概述

本项目是一个基于微服务架构的秒杀系统，采用Spring Cloud Alibaba技术栈构建。系统主要包含用户服务、商品服务、秒杀服务、订单服务和支付服务等核心模块，通过网关服务统一对外提供API接口。

## 2. 技术架构

### 2.1 整体架构

```mermaid
graph TD
    A[前端应用] --> B[网关服务]
    B --> C[用户服务]
    B --> D[商品服务]
    B --> E[秒杀服务]
    B --> F[订单服务]
    B --> G[支付服务]
    
    H[Nacos] -.-> C
    H[Nacos] -.-> D
    H[Nacos] -.-> E
    H[Nacos] -.-> F
    H[Nacos] -.-> G
    
    I[Seata] -.-> C
    I[Seata] -.-> D
    I[Seata] -.-> E
    I[Seata] -.-> F
    I[Seata] -.-> G
    
    J[Redis] -.-> C
    J[Redis] -.-> D
    J[Redis] -.-> <PERSON>
    J[Redis] -.-> F
    J[Redis] -.-> G
    
    <PERSON>[MySQL] -.-> C
    K[MySQL] -.-> D
    K[MySQL] -.-> E
    K[MySQL] -.-> F
    K[MySQL] -.-> G
```

### 2.2 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.7.18 | 微服务框架基础 |
| Spring Cloud | 2021.0.5 | 微服务治理 |
| Spring Cloud Alibaba | 2021.0.5.0 | 阿里巴巴微服务解决方案 |
| Nacos | 1.4.2 | 服务注册与配置中心 |
| Seata | 1.4.2 | 分布式事务处理 |
| MySQL | 8.0.28 | 关系型数据库 |
| MyBatis Plus | 3.5.6 | ORM框架 |
| Redis | - | 缓存和高并发处理 |
| RabbitMQ | - | 消息队列 |
| Lombok | 1.18.30 | 简化Java代码编写 |

## 3. 模块说明

### 3.1 网关服务 (gateway-service)
- 端口: 9000
- 功能: 统一入口、路由转发、权限验证

### 3.2 用户服务 (user-service)
- 端口: 8001
- 功能: 用户注册、登录、认证授权

### 3.3 商品服务 (product-service)
- 端口: 8002
- 功能: 商品信息管理

### 3.4 秒杀服务 (seckill-service)
- 端口: 8003
- 功能: 秒杀活动管理、参与秒杀

### 3.5 订单服务 (order-service)
- 端口: 8004
- 功能: 订单创建、查询

### 3.6 支付服务 (payment-service)
- 端口: 8005
- 功能: 支付处理

### 3.7 公共模块
- common-config: 公共配置
- common-dto: 公共数据传输对象
- common-exception: 公共异常处理
- common-mq: 公共消息队列处理

## 4. 核心技术实现

### 4.1 高并发处理
1. **Redis缓存**: 使用Redis存储秒杀库存，通过原子性操作保证库存扣减的准确性
2. **消息队列**: 使用RabbitMQ异步处理订单创建等耗时操作
3. **数据库优化**: 使用连接池、索引优化等手段提升数据库性能

### 4.2 安全机制
1. **JWT认证**: 使用JWT实现无状态认证
2. **OAuth2**: 实现标准的OAuth2认证授权流程
3. **网关鉴权**: 在网关层统一进行权限验证

### 4.3 分布式事务
1. **Seata集成**: 使用Seata实现分布式事务管理
2. **事务传播**: 合理设置事务传播机制

### 4.4 服务治理
1. **Nacos集成**: 使用Nacos进行服务注册与发现
2. **负载均衡**: 使用Ribbon实现客户端负载均衡
3. **熔断降级**: 使用Sentinel实现服务熔断与降级

## 5. 数据库设计

### 5.1 用户表 (t_user)
```sql
CREATE TABLE `t_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '加密密码',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0-禁用,1-正常)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 5.2 商品表 (t_product)
```sql
CREATE TABLE `t_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` varchar(500) DEFAULT NULL COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL COMMENT '原价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '普通库存',
  `image_url` varchar(255) DEFAULT NULL COMMENT '商品图片URL',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0-下架,1-上架)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
```

### 5.3 秒杀活动表 (t_seckill_activity)
```sql
CREATE TABLE `t_seckill_activity` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '秒杀活动ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `seckill_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `stock_count` int NOT NULL COMMENT '秒杀库存',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态(0-未开始,1-进行中,2-已结束)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀活动表';
```

### 5.4 订单表 (t_order)
```sql
CREATE TABLE `t_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `seckill_activity_id` bigint DEFAULT NULL COMMENT '秒杀活动ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '购买数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '订单状态(0-待支付,1-已支付,2-已取消,3-已完成)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_seckill_activity_id` (`seckill_activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

### 5.5 支付表 (t_payment)
```sql
CREATE TABLE `t_payment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `payment_method` tinyint NOT NULL COMMENT '支付方式(1-支付宝,2-微信,3-银行卡)',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '支付状态(0-待支付,1-支付成功,2-支付失败,3-已退款)',
  `payment_no` varchar(64) DEFAULT NULL COMMENT '支付流水号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付表';
```

### 5.6 秒杀订单表 (t_seckill_order)
```sql
CREATE TABLE `t_seckill_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `activity_id` bigint NOT NULL COMMENT '秒杀活动ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_activity` (`user_id`,`activity_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单表';
```

## 6. Redis设计

### 6.1 秒杀库存
- Key: `seckill:stock:{activityId}`
- Type: String
- Value: 库存数量

### 6.2 用户参与记录
- Key: `seckill:user:{activityId}:{userId}`
- Type: String
- Value: true/false

### 6.3 活动状态
- Key: `seckill:status:{activityId}`
- Type: String
- Value: 活动状态(0-未开始,1-进行中,2-已结束)

## 7. 消息队列设计

### 7.1 队列定义
1. **秒杀队列**: seckill.queue
2. **订单队列**: order.queue
3. **支付队列**: payment.queue
4. **通知队列**: notification.queue

### 7.2 交换机定义
1. **秒杀交换机**: seckill.exchange
2. **订单交换机**: order.exchange
3. **支付交换机**: payment.exchange
4. **通知交换机**: notification.exchange

### 7.3 路由键定义
1. **秒杀路由键**: seckill.routing.key
2. **订单路由键**: order.routing.key
3. **支付路由键**: payment.routing.key
4. **通知路由键**: notification.routing.key

## 8. 安全设计

### 8.1 认证机制
- 使用JWT进行无状态认证
- 采用RSA密钥对进行JWT签名和验证
- 实现OAuth2认证授权流程

### 8.2 授权机制
- 基于角色的访问控制(RBAC)
- 网关层统一权限验证
- 服务间通信安全

### 8.3 数据安全
- 用户密码使用BCrypt算法加密存储
- 敏感信息传输使用HTTPS加密
- 数据库连接使用SSL加密

## 9. 部署架构

### 9.1 服务部署
- 网关服务: 1个实例
- 用户服务: 2个实例
- 商品服务: 2个实例
- 秒杀服务: 3个实例
- 订单服务: 2个实例
- 支付服务: 2个实例

### 9.2 中间件部署
- Nacos: 1个实例(单机模式)
- Seata: 1个实例(单机模式)
- Redis: 1个实例(单机模式)
- RabbitMQ: 1个实例(单机模式)
- MySQL: 1个实例(单机模式)

## 10. 性能优化

### 10.1 数据库优化
- 合理设计索引
- 使用连接池
- SQL语句优化
- 读写分离

### 10.2 缓存优化
- Redis缓存热点数据
- 合理设置缓存过期时间
- 缓存预热
- 缓存穿透防护

### 10.3 应用优化
- 线程池配置优化
- 异步处理
- 批量操作
- 资源复用

## 11. 监控与运维

### 11.1 日志管理
- 统一日志格式
- 日志级别控制
- 日志收集与分析
- 错误日志告警

### 11.2 性能监控
- JVM监控
- 数据库监控
- Redis监控
- 消息队列监控

### 11.3 健康检查
- 服务健康检查
- 数据库连接检查
- Redis连接检查
- 消息队列连接检查