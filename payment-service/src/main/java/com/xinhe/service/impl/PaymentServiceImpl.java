package com.xinhe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.entity.Payment;
import com.xinhe.mapper.PaymentMapper;
import com.xinhe.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 支付服务实现类
 * 
 * <AUTHOR> Name
 */
@Service
public class PaymentServiceImpl extends ServiceImpl<PaymentMapper, Payment> implements PaymentService {
    
    @Autowired
    private PaymentMapper paymentMapper;
    
    @Override
    public boolean createPayment(Payment payment) {
        payment.setCreateTime(new Date());
        payment.setUpdateTime(new Date());
        return save(payment);
    }
    
    @Override
    public Payment getPaymentByOrderId(Long orderId) {
        QueryWrapper<Payment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        return paymentMapper.selectOne(queryWrapper);
    }
    
    @Override
    public boolean processPaymentResult(Long paymentId, Integer status) {
        Payment payment = new Payment();
        payment.setId(paymentId);
        payment.setStatus(status);
        payment.setPayTime(new Date());
        payment.setUpdateTime(new Date());
        return updateById(payment);
    }
}