package com.xinhe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinhe.entity.Payment;

/**
 * 支付服务接口
 * 
 * <AUTHOR> Name
 */
public interface PaymentService extends IService<Payment> {
    
    /**
     * 创建支付记录
     * 
     * @param payment 支付信息
     * @return 是否创建成功
     */
    boolean createPayment(Payment payment);
    
    /**
     * 根据订单ID获取支付信息
     * 
     * @param orderId 订单ID
     * @return 支付信息
     */
    Payment getPaymentByOrderId(Long orderId);
    
    /**
     * 处理支付结果
     * 
     * @param paymentId 支付ID
     * @param status 支付状态
     * @return 是否处理成功
     */
    boolean processPaymentResult(Long paymentId, Integer status);
}