package com.xinhe.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class PaymentVO {
    /**
     * 支付ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付方式(1-支付宝,2-微信,3-银行卡)
     */
    private Integer paymentMethod;

    /**
     * 支付方式描述
     */
    private String paymentMethodDesc;

    /**
     * 支付状态(0-待支付,1-支付成功,2-支付失败,3-已退款)
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}