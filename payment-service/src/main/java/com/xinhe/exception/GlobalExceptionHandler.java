package com.xinhe.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理系统中未捕获的异常
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理自定义业务异常
     * 
     * @param e 业务异常
     * @return 异常响应结果
     */
    @ExceptionHandler(BaseException.class)
    public Map<String, Object> handleBaseException(BaseException e) {
        log.error("业务异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", e.getCode());
        result.put("message", e.getMessage());
        return result;
    }
    
    /**
     * 处理支付异常
     * 
     * @param e 支付异常
     * @return 异常响应结果
     */
    @ExceptionHandler(PaymentException.class)
    public Map<String, Object> handlePaymentException(PaymentException e) {
        log.error("支付异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", e.getCode());
        result.put("message", e.getMessage());
        return result;
    }
    
    /**
     * 处理系统异常
     * 
     * @param e 系统异常
     * @return 异常响应结果
     */
    @ExceptionHandler(Exception.class)
    public Map<String, Object> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", 500);
        result.put("message", "系统异常: " + e.getMessage());
        return result;
    }
}