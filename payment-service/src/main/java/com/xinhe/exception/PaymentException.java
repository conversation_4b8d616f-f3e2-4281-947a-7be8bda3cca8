package com.xinhe.exception;

/**
 * 支付异常类
 *
 * <AUTHOR>
 * @since 1.0
 */
public class PaymentException extends BaseException {

    public PaymentException(String message) {
        super(600, message);
    }
    public static final int PAYMENT_NOT_FOUND = 4001;
    public static final int PAYMENT_CREATE_FAILED = 4002;
    public static final int PAYMENT_PROCESS_FAILED = 4003;
    public static final int PAYMENT_STATUS_INVALID = 4004;

    public PaymentException(int code, String message) {
        super(code, message);
    }

    public PaymentException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 支付记录未找到异常
     *
     * @return PaymentException
     */
    public static PaymentException paymentNotFound() {
        return new PaymentException(PAYMENT_NOT_FOUND, "支付记录不存在");
    }

    /**
     * 支付创建失败异常
     *
     * @return PaymentException
     */
    public static PaymentException paymentCreateFailed(String message) {
        return new PaymentException(PAYMENT_CREATE_FAILED, "支付创建失败: " + message);
    }

    /**
     * 支付处理失败异常
     *
     * @return PaymentException
     */
    public static PaymentException paymentProcessFailed(String message) {
        return new PaymentException(PAYMENT_PROCESS_FAILED, "支付处理失败: " + message);
    }

    /**
     * 支付状态无效异常
     *
     * @return PaymentException
     */
    public static PaymentException paymentStatusInvalid() {
        return new PaymentException(PAYMENT_STATUS_INVALID, "支付状态无效");
    }
}