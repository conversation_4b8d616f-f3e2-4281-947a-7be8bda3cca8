package com.xinhe.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 支付DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Data
public class PaymentDTO {
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付方式(1-支付宝,2-微信,3-银行卡)
     */
    private Integer paymentMethod;
}