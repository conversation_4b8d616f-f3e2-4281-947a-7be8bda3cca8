package com.xinhe.controller;

import com.xinhe.entity.Payment;
import com.xinhe.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付控制器
 * 
 * <AUTHOR> Name
 */
@RestController
@RequestMapping("/api/payments")
public class PaymentController {
    
    @Autowired
    private PaymentService paymentService;
    
    /**
     * 创建支付记录
     * 
     * @param payment 支付信息
     * @return 创建结果
     */
    @PostMapping
    public Map<String, Object> createPayment(@RequestBody Payment payment) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = paymentService.createPayment(payment);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "支付记录创建成功");
                Map<String, Object> data = new HashMap<>();
                data.put("paymentId", payment.getId());
                result.put("data", data);
            } else {
                result.put("code", 500);
                result.put("message", "支付记录创建失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "支付记录创建异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取支付信息
     * 
     * @param orderId 订单ID
     * @return 支付信息
     */
    @GetMapping("/order/{orderId}")
    public Map<String, Object> getPaymentByOrderId(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Payment payment = paymentService.getPaymentByOrderId(orderId);
            if (payment != null) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", payment);
            } else {
                result.put("code", 404);
                result.put("message", "支付信息不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取支付信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 处理支付结果
     * 
     * @param paymentId 支付ID
     * @param status 支付状态
     * @return 处理结果
     */
    @PutMapping("/{paymentId}/result")
    public Map<String, Object> processPaymentResult(@PathVariable Long paymentId, @RequestParam Integer status) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = paymentService.processPaymentResult(paymentId, status);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "支付结果处理成功");
            } else {
                result.put("code", 500);
                result.put("message", "支付结果处理失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "支付结果处理异常: " + e.getMessage());
        }
        
        return result;
    }
}