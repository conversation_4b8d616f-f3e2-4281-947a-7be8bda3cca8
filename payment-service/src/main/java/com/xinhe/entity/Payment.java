package com.xinhe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付实体类
 * 
 * <AUTHOR> Name
 */
@Data
@TableName("t_payment")
public class Payment {
    /**
     * 支付ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 支付方式(1-支付宝,2-微信,3-银行卡)
     */
    private Integer paymentMethod;
    
    /**
     * 支付状态(0-待支付,1-支付成功,2-支付失败,3-已退款)
     */
    private Integer status;
    
    /**
     * 支付流水号
     */
    private String paymentNo;
    
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    // 添加缺失的setter方法
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }
    
    // 添加缺失的getter方法
    public Long getId() {
        return id;
    }
}