# 用户服务接口文档

## 1. 概述

用户服务是秒杀系统中的核心服务之一，负责用户注册、认证、授权等功能。该服务基于Spring Boot、Spring Security和Spring Authorization Server构建，使用JWT进行身份验证和授权。

### 1.1 技术栈

- Spring Boot 2.x
- Spring Security 5.7+
- Spring Authorization Server
- JWT (JSON Web Tokens)
- MySQL (可选，当前版本使用内存存储)
- Redis (可选，用于令牌存储)

### 1.2 服务架构

用户服务采用无状态架构，通过JWT令牌进行身份验证和授权。服务包含以下主要组件：

- 用户管理：用户注册、信息查询
- 认证服务：OAuth2令牌颁发
- 授权服务：资源访问控制

## 2. API接口

### 2.1 用户注册接口

#### 接口地址
```
POST /api/user/register
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| phone | string | 否 | 手机号码 |
| email | string | 否 | 邮箱地址 |

#### 请求示例
```json
{
  "username": "testuser",
  "password": "123456",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 2,
    "username": "testuser",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": 1
  }
}
```

#### 错误响应示例
```json
{
  "success": false,
  "message": "用户名已存在"
}
```

### 2.2 OAuth2令牌获取接口

#### 接口地址
```
POST /oauth/token
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

#### 请求示例
```http
POST /oauth/token?username=admin&password=123456
```

#### 响应示例
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImF1ZCI6InNlaWxsLWNsaWVudCIsIm5iZiI6MTYyMzQ1NjI0NSwic2NvcGUiOlsib3BlbmlkIiwicHJvZmlsZSIsIm1lc3NhZ2UucmVhZCIsIm1lc3NhZ2Uud3JpdGUiXSwiaXNzIjoiaHR0cDovL2xvY2FsaG9zdDo5MDkwIiwiZXhwIjoxNjIzNDYzNDQ1LCJpYXQiOjE2MjM0NTYyNDV9.XXXXX",
  "token_type": "Bearer",
  "expires_in": 7200
}
```

### 2.3 获取当前用户信息接口

#### 接口地址
```
GET /api/user/profile
GET /api/user/me
```

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": 1
  }
}
```

#### 错误响应示例
```json
{
  "success": false,
  "message": "用户未认证"
}
```

### 2.4 OAuth2注册接口

#### 接口地址
```
POST /oauth/register
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| phone | string | 否 | 手机号码 |
| email | string | 否 | 邮箱地址 |

#### 请求示例
```json
{
  "username": "oauthuser",
  "password": "123456",
  "phone": "13800138001",
  "email": "<EMAIL>"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 3,
    "username": "oauthuser",
    "phone": "13800138001",
    "email": "<EMAIL>",
    "status": 1
  }
}
```

## 3. 认证与授权流程

### 3.1 用户注册流程
1. 客户端向`/api/user/register`或`/oauth/register`发送POST请求
2. 服务端验证用户名是否已存在
3. 服务端使用BCrypt对密码进行加密
4. 服务端保存用户信息
5. 返回注册成功响应

### 3.2 OAuth2认证流程
1. 客户端向`/oauth/token`发送POST请求，携带用户名和密码
2. 服务端验证用户凭据
3. 服务端生成JWT令牌
4. 返回包含访问令牌的响应

### 3.3 资源访问流程
1. 客户端在请求头中携带`Authorization: Bearer <token>`
2. 服务端验证JWT令牌的有效性
3. 服务端从令牌中提取用户信息和权限
4. 根据权限控制访问资源

## 4. 安全特性

### 4.1 密码安全
- 使用BCrypt强哈希算法加密存储密码
- 每个密码使用不同的盐值
- 防止彩虹表攻击

### 4.2 令牌安全
- 使用RSA密钥对进行JWT签名
- 令牌有过期时间限制（默认2小时）
- 支持令牌刷新机制

### 4.3 传输安全
- 支持HTTPS协议
- 禁用CSRF保护（无状态认证不需要）
- 使用无状态会话管理

### 4.4 权限控制
- 基于角色的访问控制（RBAC）
- 细粒度的URL权限控制
- 支持管理员和普通用户角色

## 5. 配置说明

### 5.1 应用配置
```yaml
server:
  port: 9090

spring:
  application:
    name: user-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  datasource:
    url: **********************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379
    timeout: 3000ms
```

### 5.2 客户端配置
默认客户端配置：
- 客户端ID：seckill-client
- 客户端密钥：client_secret
- 授权类型：authorization_code, refresh_token, client_credentials
- 重定向URI：http://127.0.0.1:8080/login/oauth2/code/messaging-client-oidc

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 7. 使用示例

### 7.1 用户注册示例
```bash
curl -X POST \
  http://localhost:9090/api/user/register \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "newuser",
    "password": "123456",
    "phone": "13800138002",
    "email": "<EMAIL>"
  }'
```

### 7.2 获取令牌示例
```bash
curl -X POST \
  'http://localhost:9090/oauth/token?username=newuser&password=123456'
```

### 7.3 访问受保护资源示例
```bash
curl -X GET \
  http://localhost:9090/api/user/profile \
  -H 'Authorization: Bearer eyJhbGciOiJSUzI1NiJ9.XXXXX'
```