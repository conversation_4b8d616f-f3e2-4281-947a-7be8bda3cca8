package com.xinhe.entity;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;

/**
 * 自定义用户详情实现类
 * 实现Spring Security的UserDetails接口，用于封装用户详细信息
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class UserDetailsImpl implements UserDetails {
    /**
     * 关联的用户实体
     */
    private User user;

    /**
     * 构造函数
     * 
     * @param user 用户实体
     */
    public UserDetailsImpl(User user) {
        this.user = user;
    }

    /**
     * 获取用户权限集合
     * 
     * @return 权限集合
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 简化处理，实际项目中应根据用户角色返回不同的权限
        if (user.getId() == 1) {
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_ADMIN"));
        } else {
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        }
    }

    /**
     * 获取用户密码
     * 
     * @return 加密后的密码
     */
    @Override
    public String getPassword() {
        return user.getPassword();
    }

    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    @Override
    public String getUsername() {
        return user.getUsername();
    }

    /**
     * 账户是否未过期
     * 
     * @return true表示未过期
     */
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 账户是否未锁定
     * 
     * @return true表示未锁定
     */
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 凭证是否未过期
     * 
     * @return true表示未过期
     */
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 账户是否启用
     * 
     * @return true表示启用
     */
    @Override
    public boolean isEnabled() {
        return user.getStatus() == 1;
    }
}