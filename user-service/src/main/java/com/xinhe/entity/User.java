package com.xinhe.entity;

import lombok.Data;
import java.io.Serializable;

/**
 * 用户实体类
 * 用于表示系统中的用户信息
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID，主键
     */
    private Long id;
    
    /**
     * 用户名，用于登录
     */
    private String username;
    
    /**
     * 密码，存储加密后的密码
     */
    private String password;
    
    /**
     * 手机号码
     */
    private String phone;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 用户状态：0-禁用 1-正常
     */
    private Integer status;
    
}