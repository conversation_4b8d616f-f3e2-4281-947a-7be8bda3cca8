package com.xinhe.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT工具类
 * 提供JWT令牌的生成、解析和验证功能
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
public class JwtUtil {

    // 默认过期时间：24小时
    public static final long JWT_TOKEN_VALIDITY = 24 * 60 * 60;

    // 默认密钥，建议在application.properties中配置更安全的密钥
    private static final String DEFAULT_SECRET = "seckill_secret_key_which_is_long_enough_for_hs512_algorithm";

    @Value("${jwt.secret:" + DEFAULT_SECRET + "}")
    private String secret;

    // 存储密钥的签名密钥对象
    private SecretKey signingKey;

    /**
     * 从token中获取用户名
     *
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            return getClaimFromToken(token, Claims::getSubject);
        } catch (Exception e) {
            log.error("从token中获取用户名失败", e);
            return null;
        }
    }

    /**
     * 从token中获取过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            return getClaimFromToken(token, Claims::getExpiration);
        } catch (Exception e) {
            log.error("从token中获取过期时间失败", e);
            return null;
        }
    }

    /**
     * 从token中获取自定义声明
     *
     * @param token          JWT令牌
     * @param claimsResolver 声明解析器
     * @param <T>            声明类型
     * @return 声明值
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        try {
            final Claims claims = getAllClaimsFromToken(token);
            return claimsResolver.apply(claims);
        } catch (Exception e) {
            log.error("从token中获取声明失败", e);
            return null;
        }
    }

    /**
     * 从token中获取所有声明
     *
     * @param token JWT令牌
     * @return 所有声明
     */
    private Claims getAllClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.warn("Token已过期: {}", token);
            throw e;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的token: {}", token);
            throw e;
        } catch (MalformedJwtException e) {
            log.warn("格式错误的token: {}", token);
            throw e;
        } catch (SignatureException e) {
            log.warn("token签名无效: {}", token);
            throw e;
        } catch (IllegalArgumentException e) {
            log.warn("非法参数: {}", token);
            throw e;
        }
    }

    /**
     * 检查token是否过期
     *
     * @param token JWT令牌
     * @return 是否过期
     */
    private Boolean isTokenExpired(String token) {
        try {
            final Date expiration = getExpirationDateFromToken(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查token是否过期时发生异常", e);
            return true;
        }
    }

    /**
     * 生成token
     * 
     * @param userDetails 用户详情
     * @return JWT令牌
     */
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        return doGenerateToken(claims, userDetails.getUsername());
    }

    /**
     * 生成token（支持用户ID和用户名）
     *
     * @param userId   用户ID
     * @param username 用户名
     * @return JWT令牌
     */
    public String generateToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        return doGenerateToken(claims, username);
    }

    /**
     * 生成token的实际方法
     *
     * @param claims  声明
     * @param subject 主题（用户名）
     * @return JWT令牌
     */
    private String doGenerateToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + JWT_TOKEN_VALIDITY * 1000))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 验证token
     *
     * @param token       JWT令牌
     * @param userDetails 用户详情
     * @return 是否有效
     */
    public Boolean validateToken(String token, UserDetails userDetails) {
        try {
            log.debug("开始验证token: {}", token);
            final String username = getUsernameFromToken(token);
            
            if (username == null) {
                log.warn("无法从token中获取用户名");
                return false;
            }
            
            if (!username.equals(userDetails.getUsername())) {
                log.warn("用户名不匹配: token中的用户名={}, userDetails中的用户名={}", username, userDetails.getUsername());
                return false;
            }
            
            if (isTokenExpired(token)) {
                log.warn("token已过期");
                return false;
            }
            
            log.info("token验证成功: {}", token);
            return true;
        } catch (Exception e) {
            log.error("验证token时发生异常: {}", token, e);
            return false;
        }
    }

    /**
     * 检查token是否有效（不验证用户）
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public Boolean isTokenValid(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            log.error("检查token是否有效时发生异常", e);
            return false;
        }
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        if (signingKey == null) {
            signingKey = Keys.hmacShaKeyFor(secret.getBytes());
            log.debug("创建新的签名密钥实例");
        }
        return signingKey;
    }

    /**
     * 设置新的密钥，用于动态更新密钥
     *
     * @param newSecret 新的密钥
     */
    public void updateSecret(String newSecret) {
        this.secret = newSecret;
        this.signingKey = Keys.hmacShaKeyFor(newSecret.getBytes());
        log.info("JWT签名密钥已更新");
    }
}
