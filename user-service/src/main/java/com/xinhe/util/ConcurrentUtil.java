package com.xinhe.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高并发工具类
 * 提供原子操作和并发安全的数据结构
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Component
public class ConcurrentUtil {

    /**
     * 用户登录计数器
     * key: userId, value: 登录次数计数器
     */
    private final ConcurrentHashMap<Long, AtomicInteger> userLoginCount = new ConcurrentHashMap<>();

    /**
     * 请求ID生成器
     */
    private final AtomicLong requestIdGenerator = new AtomicLong(System.currentTimeMillis());

    /**
     * 获取或创建用户登录计数器
     *
     * @param userId 用户ID
     * @return AtomicInteger 计数器
     */
    public AtomicInteger getUserLoginCounter(Long userId) {
        return userLoginCount.computeIfAbsent(userId, k -> new AtomicInteger(0));
    }

    /**
     * 增加用户登录计数
     *
     * @param userId 用户ID
     * @return int 当前登录次数
     */
    public int incrementUserLoginCount(Long userId) {
        return getUserLoginCounter(userId).incrementAndGet();
    }

    /**
     * 生成请求ID
     *
     * @return long 请求ID
     */
    public long generateRequestId() {
        return requestIdGenerator.incrementAndGet();
    }

    /**
     * 获取当前登录次数
     *
     * @param userId 用户ID
     * @return int 当前登录次数
     */
    public int getCurrentLoginCount(Long userId) {
        AtomicInteger counter = userLoginCount.get(userId);
        return counter != null ? counter.get() : 0;
    }
}