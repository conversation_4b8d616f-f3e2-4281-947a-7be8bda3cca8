package com.xinhe.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsent;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.security.Principal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OAuth2授权确认控制器
 * 处理OAuth2授权确认页面相关请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-09
 */
@Slf4j
@Controller
@RequestMapping("/oauth2")
public class AuthorizationConsentController {

    @Autowired
    private RegisteredClientRepository registeredClientRepository;

    @Autowired
    private OAuth2AuthorizationConsentService authorizationConsentService;

    /**
     * OAuth2授权确认页面
     * 当客户端请求授权时，用户需要确认授权范围
     *
     * @param principal 当前认证用户
     * @param model 模型对象
     * @param clientId 客户端ID
     * @param scope 请求的授权范围
     * @param state 状态参数
     * @return 授权确认页面
     */
    @GetMapping("/authorize")
    public String authorizationConsent(Principal principal, Model model,
                                       @RequestParam(OAuth2ParameterNames.CLIENT_ID) String clientId,
                                       @RequestParam(OAuth2ParameterNames.SCOPE) String scope,
                                       @RequestParam(OAuth2ParameterNames.STATE) String state) {

        // 获取客户端信息
        RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
        if (registeredClient == null) {
            throw new IllegalArgumentException("客户端未找到: " + clientId);
        }

        // 获取已授权范围
        OAuth2AuthorizationConsent currentAuthorizationConsent =
                authorizationConsentService.findById(registeredClient.getId(), principal.getName());
        
        // 解析请求的范围
        Set<String> scopes = Arrays.stream(scope.split(" ")).collect(Collectors.toSet());
        
        // 过滤出需要用户确认的范围（OIDC范围除外）
        Set<String> scopesToApprove = new HashSet<>();
        Set<String> previouslyApprovedScopes = new HashSet<>();
        
        for (String requestedScope : scopes) {
            if (OidcScopes.OPENID.equals(requestedScope)) {
                continue;
            }
            if (currentAuthorizationConsent != null && 
                currentAuthorizationConsent.getScopes().contains(requestedScope)) {
                previouslyApprovedScopes.add(requestedScope);
            } else {
                scopesToApprove.add(requestedScope);
            }
        }

        model.addAttribute("clientId", clientId);
        model.addAttribute("state", state);
        model.addAttribute("scopes", scopesToApprove);
        model.addAttribute("principalName", principal.getName());
        
        return "oauth2/consent";
    }
}