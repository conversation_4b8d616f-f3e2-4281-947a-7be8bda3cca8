package com.xinhe.controller;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.RSAKey;
import com.xinhe.exception.UserException;
import com.xinhe.service.UserService;
import com.xinhe.util.JwtUtil;
import com.xinhe.vo.UserLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsent;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.*;

/**
 * OAuth2授权控制器
 * 处理OAuth2授权流程
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Slf4j
@Controller
@RequestMapping("/oauth2")
public class OAuth2AuthorizationController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RSAKey rsaKey;

    @Autowired
    private RegisteredClientRepository registeredClientRepository;

    @Autowired
    private OAuth2AuthorizationConsentService authorizationConsentService;

    /**
     * OAuth2授权确认页面
     * 当客户端请求授权时，用户需要确认授权范围
     *
     * @param principal 当前认证用户
     * @param model 模型对象
     * @param clientId 客户端ID
     * @param scope 请求的授权范围
     * @param state 状态参数
     * @return 授权确认页面
     */
    @GetMapping("/authorize")
    public String authorizationConsent(Principal principal, Model model,
                                   @RequestParam(OAuth2ParameterNames.CLIENT_ID) String clientId,
                                   @RequestParam(OAuth2ParameterNames.SCOPE) String scope,
                                   @RequestParam(OAuth2ParameterNames.STATE) String state) {

        // 获取客户端信息
        RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
        if (registeredClient == null) {
            throw new IllegalArgumentException("客户端未找到: " + clientId);
        }

        // 获取已授权范围
        OAuth2AuthorizationConsent currentAuthorizationConsent =
                authorizationConsentService.findById(registeredClient.getId(), principal.getName());
        
        // 解析请求的范围
        Set<String> scopes = Arrays.stream(scope.split(" ")).collect(Collectors.toSet());
        
        // 过滤出需要用户确认的范围（OIDC范围除外）
        Set<String> scopesToApprove = new HashSet<>();
        Set<String> previouslyApprovedScopes = new HashSet<>();
        
        for (String requestedScope : scopes) {
            if (OidcScopes.OPENID.equals(requestedScope)) {
                continue;
            }
            if (currentAuthorizationConsent != null && 
                currentAuthorizationConsent.getScopes().contains(requestedScope)) {
                previouslyApprovedScopes.add(requestedScope);
            } else {
                scopesToApprove.add(requestedScope);
            }
        }

        model.addAttribute("clientId", clientId);
        model.addAttribute("state", state);
        model.addAttribute("scopes", scopesToApprove);
        model.addAttribute("principalName", principal.getName());
        
        return "oauth2/consent";
    }
}
package com.xinhe.controller;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.xinhe.dto.UserLoginDTO;
import com.xinhe.dto.UserRegisterDTO;
import com.xinhe.entity.User;
import com.xinhe.exception.UserException;
import com.xinhe.service.UserService;
import com.xinhe.util.JwtUtil;
import com.xinhe.vo.UserLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsent;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OAuth2认证控制器
 * 提供用户登录、注册、获取公钥等认证相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Slf4j
@Controller
@RequestMapping("/oauth2")
public class OAuth2Controller {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RSAKey rsaKey;

    @Autowired
    private RegisteredClientRepository registeredClientRepository;

    @Autowired
    private OAuth2AuthorizationConsentService authorizationConsentService;

    /**
     * OAuth2授权确认页面
     * 当客户端请求授权时，用户需要确认授权范围
     *
     * @param principal 当前认证用户
     * @param model 模型对象
     * @param clientId 客户端ID
     * @param scope 请求的授权范围
     * @param state 状态参数
     * @return 授权确认页面
     */
    @GetMapping("/authorize")
    public String authorizationConsent(Principal principal, Model model,
                                       @RequestParam(OAuth2ParameterNames.CLIENT_ID) String clientId,
                                       @RequestParam(OAuth2ParameterNames.SCOPE) String scope,
                                       @RequestParam(OAuth2ParameterNames.STATE) String state) {

        // 获取客户端信息
        RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
        if (registeredClient == null) {
            throw new IllegalArgumentException("客户端未找到: " + clientId);
        }

        // 获取已授权范围
        OAuth2AuthorizationConsent currentAuthorizationConsent =
                authorizationConsentService.findById(registeredClient.getId(), principal.getName());
        
        // 解析请求的范围
        Set<String> scopes = Arrays.stream(scope.split(" ")).collect(Collectors.toSet());
        
        // 过滤出需要用户确认的范围（OIDC范围除外）
        Set<String> scopesToApprove = new HashSet<>();
        Set<String> previouslyApprovedScopes = new HashSet<>();
        
        for (String requestedScope : scopes) {
            if (OidcScopes.OPENID.equals(requestedScope)) {
                continue;
            }
            if (currentAuthorizationConsent != null && 
                currentAuthorizationConsent.getScopes().contains(requestedScope)) {
                previouslyApprovedScopes.add(requestedScope);
            } else {
                scopesToApprove.add(requestedScope);
            }
        }

        model.addAttribute("clientId", clientId);
        model.addAttribute("state", state);
        model.addAttribute("scopes", scopesToApprove);
        model.addAttribute("principalName", principal.getName());
        
        return "oauth2/consent";
    }

    @RestController
    @RequestMapping("/oauth")
    static class OAuth2RestController {
        
        @Autowired
        private UserService userService;

        @Autowired
        private JwtUtil jwtUtil;

        @Autowired
        private PasswordEncoder passwordEncoder;

        /**
         * 用户登录接口
         *
         * @param dto 登录请求参数
         * @return 登录结果
         */
        @PostMapping("/token")
        public ResponseEntity<Map<String, Object>> login(@RequestBody UserLoginDTO dto) {
            log.info("用户登录请求: username={}", dto.getUsername());

            try {
                // 验证用户凭据
                User user = userService.findByUsername(dto.getUsername());
                if (user == null) {
                    log.warn("用户登录失败: 用户名不存在 username={}", dto.getUsername());
                    throw UserException.userNotFound();
                }

                if (!passwordEncoder.matches(dto.getPassword(), user.getPassword())) {
                    log.warn("用户登录失败: 密码错误 username={}", dto.getUsername());
                    throw UserException.invalidCredentials();
                }

                if (user.getStatus() != null && user.getStatus() == 0) {
                    log.warn("用户登录失败: 用户被禁用 username={}", dto.getUsername());
                    throw UserException.userDisabled();
                }

                // 生成JWT令牌
                String accessToken = jwtUtil.generateToken(user.getId(), user.getUsername());
                
                UserLoginVO vo = new UserLoginVO();
                vo.setAccessToken(accessToken);
                vo.setTokenType("Bearer");
                vo.setUserId(user.getId());
                vo.setUsername(user.getUsername());

                Map<String, Object> result = new HashMap<>();
                result.put("code", 200);
                result.put("message", "登录成功");
                result.put("data", vo);

                log.info("用户登录成功: username={}, userId={}", user.getUsername(), user.getId());
                return ResponseEntity.ok(result);
            } catch (UserException e) {
                log.error("用户登录异常: ", e);
                throw e;
            } catch (Exception e) {
                log.error("用户登录系统异常: ", e);
                throw new RuntimeException("登录失败", e);
            }
        }


        /**
         * 用户注册接口
         *
         * @param dto 注册请求参数
         * @return 注册结果
         */
        @PostMapping("/register")
        public ResponseEntity<Map<String, Object>> register(@RequestBody UserRegisterDTO dto) {
            log.info("用户注册请求: username={}", dto.getUsername());

            try {
                // 检查用户名是否已存在
                User existingUser = userService.findByUsername(dto.getUsername());
                if (existingUser != null) {
                    log.warn("用户注册失败: 用户名已存在 username={}", dto.getUsername());
                    throw UserException.userAlreadyExists();
                }

                // 创建新用户
                User user = new User();
                user.setUsername(dto.getUsername());
                user.setPassword(passwordEncoder.encode(dto.getPassword()));
                user.setPhone(dto.getPhone());
                user.setEmail(dto.getEmail());
                user.setStatus(1); // 默认启用

                boolean saved = userService.saveUser(user);
                if (!saved) {
                    log.error("用户注册失败: 保存用户失败 username={}", dto.getUsername());
                    throw new RuntimeException("注册失败");
                }

                Map<String, Object> result = new HashMap<>();
                result.put("code", 200);
                result.put("message", "注册成功");
                result.put("data", user.getId());

                log.info("用户注册成功: username={}, userId={}", user.getUsername(), user.getId());
                return ResponseEntity.ok(result);
            } catch (UserException e) {
                log.error("用户注册异常: ", e);
                throw e;
            } catch (Exception e) {
                log.error("用户注册系统异常: ", e);
                throw new RuntimeException("注册失败", e);
            }
    /**
     * 根据用户ID获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/user/{id}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long id) {
            log.info("获取用户信息请求: userId={}", id);

            User user = userService.findById(id);
            if (user == null) {
                log.warn("用户未找到: userId={}", id);
                Map<String, Object> result = new HashMap<>();
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.status(404).body(result);
            }

            // 移除敏感字段
            user.setPassword(null);

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", user);

            log.info("成功返回用户信息: userId={}", id);
            return ResponseEntity.ok(result);
    }

    /**
     * 获取JWK Set端点
     * 用于提供JWT令牌验证所需的公钥
     * 
     * @return JWKSet响应
     */
    @GetMapping("/jwks")
    public Map<String, Object> jwks() {
        return new JWKSet(rsaKey).toJSONObject();
    }
}