package com.xinhe.controller;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.xinhe.dto.UserLoginDTO;
import com.xinhe.dto.UserRegisterDTO;
import com.xinhe.entity.User;
import com.xinhe.exception.UserException;
import com.xinhe.service.UserService;
import com.xinhe.util.JwtUtil;
import com.xinhe.vo.UserLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2认证控制器
 * 提供用户登录、注册、获取公钥等认证相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
@Slf4j
@RestController
@RequestMapping("/oauth")
public class OAuth2Controller {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RSAKey rsaKey;

    /**
     * 用户登录接口
     *
     * @param dto 登录请求参数
     * @return 登录结果
     */
    @PostMapping("/token")
    public ResponseEntity<Map<String, Object>> login(@RequestBody UserLoginDTO dto) {
        log.info("用户登录请求: username={}", dto.getUsername());

        try {
            // 验证用户凭据
            User user = userService.findByUsername(dto.getUsername());
            if (user == null) {
                log.warn("用户登录失败: 用户名不存在 username={}", dto.getUsername());
                throw UserException.userNotFound();
            }

            if (!passwordEncoder.matches(dto.getPassword(), user.getPassword())) {
                log.warn("用户登录失败: 密码错误 username={}", dto.getUsername());
                throw UserException.invalidCredentials();
            }

            if (user.getStatus() != null && user.getStatus() == 0) {
                log.warn("用户登录失败: 用户被禁用 username={}", dto.getUsername());
                throw UserException.userDisabled();
            }

            // 生成JWT令牌
            String accessToken = jwtUtil.generateToken(user.getId(), user.getUsername());
            
            UserLoginVO vo = new UserLoginVO();
            vo.setAccessToken(accessToken);
            vo.setTokenType("Bearer");
            vo.setUserId(user.getId());
            vo.setUsername(user.getUsername());

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("data", vo);

            log.info("用户登录成功: username={}, userId={}", user.getUsername(), user.getId());
            return ResponseEntity.ok(result);
        } catch (UserException e) {
            log.error("用户登录异常: ", e);
            throw e;
        } catch (Exception e) {
            log.error("用户登录系统异常: ", e);
            throw new RuntimeException("登录失败", e);
        }
    }


    /**
     * 用户注册接口
     *
     * @param dto 注册请求参数
     * @return 注册结果
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody UserRegisterDTO dto) {
        log.info("用户注册请求: username={}", dto.getUsername());

        try {
            // 检查用户名是否已存在
            User existingUser = userService.findByUsername(dto.getUsername());
            if (existingUser != null) {
                log.warn("用户注册失败: 用户名已存在 username={}", dto.getUsername());
                throw UserException.userAlreadyExists();
            }

            // 创建新用户
            User user = new User();
            user.setUsername(dto.getUsername());
            user.setPassword(passwordEncoder.encode(dto.getPassword()));
            user.setPhone(dto.getPhone());
            user.setEmail(dto.getEmail());
            user.setStatus(1); // 默认启用

            boolean saved = userService.saveUser(user);
            if (!saved) {
                log.error("用户注册失败: 保存用户失败 username={}", dto.getUsername());
                throw new RuntimeException("注册失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "注册成功");
            result.put("data", user.getId());

            log.info("用户注册成功: username={}, userId={}", user.getUsername(), user.getId());
            return ResponseEntity.ok(result);
        } catch (UserException e) {
            log.error("用户注册异常: ", e);
            throw e;
        } catch (Exception e) {
            log.error("用户注册系统异常: ", e);
            throw new RuntimeException("注册失败", e);
        }
    }
}