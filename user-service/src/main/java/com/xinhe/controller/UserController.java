package com.xinhe.controller;

import com.xinhe.entity.User;
import com.xinhe.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 处理用户相关的请求接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-09
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册接口
     *
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody User user) {
        log.info("用户注册请求: username={}", user.getUsername());

        try {
            // 检查用户名是否已存在
            User existingUser = userService.findByUsername(user.getUsername());
            if (existingUser != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("code", 400);
                result.put("message", "用户名已存在");
                return ResponseEntity.badRequest().body(result);
            }

            // 创建新用户
            User registeredUser = userService.register(user);

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "注册成功");
            result.put("data", registeredUser);

            log.info("用户注册成功: username={}, userId={}", user.getUsername(), user.getId());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("用户注册系统异常: ", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "注册失败");
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 获取用户信息接口
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long id) {
        log.info("获取用户信息请求: userId={}", id);

        try {
            User user = userService.findById(id);
            if (user == null) {
                log.warn("用户未找到: userId={}", id);
                Map<String, Object> result = new HashMap<>();
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.status(404).body(result);
            }

            // 移除敏感字段
            user.setPassword(null);

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", user);

            log.info("成功返回用户信息: userId={}", id);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户信息异常: ", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "系统异常");
            return ResponseEntity.status(500).body(result);
        }
    }
}