package com.xinhe.controller;

import com.xinhe.entity.User;
import com.xinhe.entity.UserDetailsImpl;
import com.xinhe.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 处理用户注册、登录、获取用户信息等请求
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 用户注册接口
     * 接收用户注册信息，进行用户名唯一性检查，加密密码后保存用户信息
     *
     * @param user 用户注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查用户名是否已存在
        User existingUser = userService.findByUsername(user.getUsername());
        if (existingUser != null) {
            result.put("success", false);
            result.put("message", "用户名已存在");
            return ResponseEntity.badRequest().body(result);
        }
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 注册用户
        User registeredUser = userService.register(user);
        
        result.put("success", true);
        result.put("message", "注册成功");
        result.put("data", registeredUser);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取当前认证用户信息接口
     * 从Authentication对象中获取当前认证用户的信息
     *
     * @param authentication 认证信息
     * @return 当前认证用户信息
     */
    @GetMapping({"/profile", "/me"})
    public ResponseEntity<Map<String, Object>> getCurrentUser(Authentication authentication) {
        Map<String, Object> result = new HashMap<>();

        if (authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            User user = userDetails.getUser();
            
            // 不返回密码
            user.setPassword(null);
            
            result.put("success", true);
            result.put("data", user);
            return ResponseEntity.ok(result);
        }

        result.put("success", false);
        result.put("message", "用户未认证");
        return ResponseEntity.badRequest().body(result);
    }
}