package com.xinhe.service;

import com.xinhe.entity.User;

/**
 * 用户服务接口
 * 定义用户相关的业务操作
 * 
 * <AUTHOR>
 * @since 1.0
 */
public interface UserService {
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户对象，如果未找到返回null
     */
    User findByUsername(String username);
    
    /**
     * 用户注册
     * 
     * @param user 用户信息
     * @return 注册后的用户对象
     */
    User register(User user);
    
    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户对象，如果未找到返回null
     */
    User findById(Long id);
    
    /**
     * 保存用户信息
     *
     * @param user 用户信息
     * @return 是否保存成功
     */
    boolean saveUser(User user);
}