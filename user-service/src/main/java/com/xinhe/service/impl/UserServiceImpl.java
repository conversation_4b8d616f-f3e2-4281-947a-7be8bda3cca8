package com.xinhe.service.impl;

import com.xinhe.entity.User;
import com.xinhe.mapper.UserMapper;
import com.xinhe.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 * 提供用户相关操作的具体实现
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户实体，如果未找到返回null
     */
    @Override
    public User findByUsername(String username) {
        // 使用UserMapper从数据库查询
        return userMapper.findByUsername(username);
    }
    
    @Override
    public User register(User user) {
        // 使用UserMapper保存用户到数据库
        userMapper.insertUser(user);
        log.info("用户注册成功: username={}, userId={}", user.getUsername(), user.getId());
        return user;
    }
    
    @Override
    public User findById(Long id) {
        // 使用UserMapper根据ID查找用户
        return userMapper.findById(id);
    }
    
    @Override
    public boolean saveUser(User user) {
        // 使用UserMapper保存或更新用户信息
        if (user.getId() == null) {
            // 新用户，插入数据库
            return userMapper.insertUser(user) > 0;
        } else {
            // 已有用户，更新数据库
            return userMapper.updateUser(user) > 0;
        }
    }
}