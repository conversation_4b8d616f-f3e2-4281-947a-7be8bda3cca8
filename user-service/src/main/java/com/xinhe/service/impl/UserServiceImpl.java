package com.xinhe.service.impl;

import com.xinhe.entity.User;
import com.xinhe.service.UserService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户服务实现类
 * 提供用户相关操作的具体实现
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class UserServiceImpl implements UserService {
    
    /**
     * 模拟数据库存储用户信息（线程安全）
     */
    private Map<String, User> userMap = new ConcurrentHashMap<>();
    
    /**
     * 按用户ID存储的用户映射（线程安全）
     */
    private Map<Long, User> userIdMap = new ConcurrentHashMap<>();
    
    /**
     * 用户ID生成器
     */
    private AtomicLong userIdGenerator = new AtomicLong(2);
    
    /**
     * 初始化方法
     * 初始化一个管理员用户
     */
    @PostConstruct
    public void init() {
        // 初始化一个管理员用户
        User admin = new User();
        admin.setId(1L);
        admin.setUsername("admin");
        // 密码为 123456，这里使用BCrypt加密后的值
        admin.setPassword("$2a$10$3wznu66Q7VlzRejqVbHa.e3C0Em76pQf9cqUh7PC9f3dQD1gVhUSa");
        admin.setPhone("13800138000");
        admin.setEmail("<EMAIL>");
        admin.setStatus(1);
        
        userMap.put("admin", admin);
        userIdMap.put(1L, admin);
    }

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户实体，如果未找到返回null
     */
    @Override
    public User findByUsername(String username) {
        return userMap.get(username);
    }
    
    /**
     * 用户注册
     * 
     * @param user 用户实体
     * @return 注册后的用户实体
     */
    @Override
    public User register(User user) {
        Long userId = userIdGenerator.getAndIncrement();
        user.setId(userId);
        user.setStatus(1); // 默认启用
        
        userMap.put(user.getUsername(), user);
        userIdMap.put(userId, user);
        
        return user;
    }
    
    /**
     * 根据ID查找用户
     * 
     * @param id 用户ID
     * @return 用户实体，如果未找到返回null
     */
    @Override
    public User findById(Long id) {
        return userIdMap.get(id);
    }
    
    /**
     * 保存用户
     * 
     * @param user 用户实体
     * @return 是否保存成功
     */
    @Override
    public boolean saveUser(User user) {
        if (user.getId() == null) {
            // 新用户
            Long userId = userIdGenerator.getAndIncrement();
            user.setId(userId);
        }
        
        userMap.put(user.getUsername(), user);
        userIdMap.put(user.getId(), user);
        
        return true;
    }
}