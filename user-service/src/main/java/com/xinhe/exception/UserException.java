package com.xinhe.exception;

/**
 * 用户相关异常类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-05
 */
public class UserException extends BaseException {
    public static final int USER_NOT_FOUND = 2001;
    public static final int USER_ALREADY_EXISTS = 2002;
    public static final int INVALID_CREDENTIALS = 2003;
    public static final int USER_DISABLED = 2004;
    public static final int TOKEN_INVALID = 2005;
    public static final int TOKEN_EXPIRED = 2006;

    public UserException(int code, String message) {
        super(code, message);
    }

    public UserException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 用户未找到异常
     *
     * @return UserException
     */
    public static UserException userNotFound() {
        return new UserException(USER_NOT_FOUND, "用户不存在");
    }

    /**
     * 用户已存在异常
     *
     * @return UserException
     */
    public static UserException userAlreadyExists() {
        return new UserException(USER_ALREADY_EXISTS, "用户已存在");
    }

    /**
     * 无效凭证异常
     *
     * @return UserException
     */
    public static UserException invalidCredentials() {
        return new UserException(INVALID_CREDENTIALS, "用户名或密码错误");
    }

    /**
     * 用户被禁用异常
     *
     * @return UserException
     */
    public static UserException userDisabled() {
        return new UserException(USER_DISABLED, "用户已被禁用");
    }

    /**
     * 无效令牌异常
     *
     * @return UserException
     */
    public static UserException tokenInvalid() {
        return new UserException(TOKEN_INVALID, "令牌无效");
    }

    /**
     * 令牌过期异常
     *
     * @return UserException
     */
    public static UserException tokenExpired() {
        return new UserException(TOKEN_EXPIRED, "令牌已过期");
    }
}