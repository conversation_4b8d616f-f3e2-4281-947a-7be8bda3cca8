<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权确认</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .client-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .scope-list {
            margin: 20px 0;
        }
        .scope-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .scope-item:last-child {
            border-bottom: none;
        }
        .buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>授权确认</h1>
    
    <div class="client-info">
        <h3>应用程序请求访问您的账户</h3>
        <p>客户端ID: <strong th:text="${clientId}"></strong></p>
        <p>用户: <strong th:text="${principalName}"></strong></p>
    </div>
    
    <div class="scope-list">
        <h3>请求的权限:</h3>
        <form method="post" th:action="@{/oauth2/authorize}">
            <input type="hidden" name="client_id" th:value="${clientId}">
            <input type="hidden" name="state" th:value="${state}">
            
            <div th:each="scope : ${scopes}" class="scope-item">
                <label>
                    <input type="checkbox" name="scope" th:value="${scope}" checked>
                    <span th:text="${scope}"></span>
                </label>
            </div>
            
            <div class="buttons">
                <button type="submit" name="user_oauth_approval" value="true" class="btn btn-primary">授权</button>
                <button type="submit" name="user_oauth_approval" value="false" class="btn btn-secondary">拒绝</button>
            </div>
        </form>
    </div>
</div>
</body>
</html>