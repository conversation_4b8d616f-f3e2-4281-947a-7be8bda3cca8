-- OAuth2客户端详情表
CREATE TABLE IF NOT EXISTS `oauth_client_details` (
  `client_id` varchar(256) NOT NULL COMMENT '客户端ID',
  `resource_ids` varchar(256) DEFAULT NULL COMMENT '资源ID集合',
  `client_secret` varchar(256) DEFAULT NULL COMMENT '客户端密钥',
  `scope` varchar(256) DEFAULT NULL COMMENT '权限范围',
  `authorized_grant_types` varchar(256) DEFAULT NULL COMMENT '授权类型',
  `web_server_redirect_uri` varchar(256) DEFAULT NULL COMMENT '重定向URI',
  `authorities` varchar(256) DEFAULT NULL COMMENT '权限',
  `access_token_validity` int(11) DEFAULT NULL COMMENT '访问令牌有效期',
  `refresh_token_validity` int(11) DEFAULT NULL COMMENT '刷新令牌有效期',
  `additional_information` varchar(4096) DEFAULT NULL COMMENT '附加信息',
  `autoapprove` varchar(256) DEFAULT NULL COMMENT '自动授权',
  PRIMARY KEY (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth2客户端详情表';

-- 插入默认客户端配置
INSERT INTO `oauth_client_details` 
(`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, 
 `web_server_redirect_uri`, `authorities`, `access_token_validity`, 
 `refresh_token_validity`, `additional_information`, `autoapprove`) 
VALUES 
('seckill-client', 'resource-server', 
 '$2a$12$YQiQxpabO6eh7rMQJxjZWuJ9U7HgLOoqHrwpstJIM6pTKtbDzlqIe', -- client_secret
 'read,write', 'authorization_code,password,refresh_token,client_credentials', 
 'http://localhost:8080/callback', 'ROLE_CLIENT', 7200, 259200, 
 '{"description":"秒杀系统客户端"}', 'true')
ON DUPLICATE KEY UPDATE 
  `resource_ids` = VALUES(`resource_ids`),
  `client_secret` = VALUES(`client_secret`),
  `scope` = VALUES(`scope`),
  `authorized_grant_types` = VALUES(`authorized_grant_types`),
  `web_server_redirect_uri` = VALUES(`web_server_redirect_uri`),
  `authorities` = VALUES(`authorities`),
  `access_token_validity` = VALUES(`access_token_validity`),
  `refresh_token_validity` = VALUES(`refresh_token_validity`),
  `additional_information` = VALUES(`additional_information`),
  `autoapprove` = VALUES(`autoapprove`);

-- 插入测试客户端配置
INSERT INTO `oauth_client_details` 
(`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, 
 `web_server_redirect_uri`, `authorities`, `access_token_validity`, 
 `refresh_token_validity`, `additional_information`, `autoapprove`) 
VALUES 
('test-client', 'resource-server', 
 '$2a$12$YQiQxpabO6eh7rMQJxjZWuJ9U7HgLOoqHrwpstJIM6pTKtbDzlqIe', -- test_secret
 'read,write', 'password,refresh_token', 
 NULL, 'ROLE_CLIENT', 3600, 86400, 
 '{"description":"测试客户端"}', 'true')
ON DUPLICATE KEY UPDATE 
  `resource_ids` = VALUES(`resource_ids`),
  `client_secret` = VALUES(`client_secret`),
  `scope` = VALUES(`scope`),
  `authorized_grant_types` = VALUES(`authorized_grant_types`),
  `authorities` = VALUES(`authorities`),
  `access_token_validity` = VALUES(`access_token_validity`),
  `refresh_token_validity` = VALUES(`refresh_token_validity`),
  `additional_information` = VALUES(`additional_information`),
  `autoapprove` = VALUES(`autoapprove`);
