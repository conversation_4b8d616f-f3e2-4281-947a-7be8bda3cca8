package com.xinhe.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinhe.entity.Order;

/**
 * 订单服务接口
 * 
 * <AUTHOR> Name
 */
public interface OrderService extends IService<Order> {
    
    /**
     * 创建订单
     * 
     * @param order 订单信息
     * @return 是否创建成功
     */
    boolean createOrder(Order order);
    
    /**
     * 根据用户ID获取订单列表
     * 
     * @param page 分页对象
     * @param userId 用户ID
     * @return 订单分页数据
     */
    IPage<Order> getOrdersByUserId(Page<Order> page, Long userId);
    
    /**
     * 根据订单ID获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情
     */
    Order getOrderDetail(Long orderId);
    
    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 订单状态
     * @return 是否更新成功
     */
    boolean updateOrderStatus(Long orderId, Integer status);
}