package com.xinhe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.entity.Order;
import com.xinhe.mapper.OrderMapper;
import com.xinhe.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 订单服务实现类
 * 
 * <AUTHOR> Name
 */
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Override
    public boolean createOrder(Order order) {
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        return save(order);
    }
    
    @Override
    public IPage<Order> getOrdersByUserId(Page<Order> page, Long userId) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time");
        return orderMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    public Order getOrderDetail(Long orderId) {
        return orderMapper.selectById(orderId);
    }
    
    @Override
    public boolean updateOrderStatus(Long orderId, Integer status) {
        Order order = new Order();
        order.setId(orderId);
        order.setStatus(status);
        order.setUpdateTime(new Date());
        return updateById(order);
    }
}