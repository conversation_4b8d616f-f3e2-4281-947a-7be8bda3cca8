package com.xinhe.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义异常基类
 * 所有业务异常都应该继承此类
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseException extends RuntimeException {
    
    /**
     * 异常代码
     */
    private Integer code;
    
    /**
     * 异常消息
     */
    private String message;
    
    /**
     * 构造函数
     * 
     * @param code 异常代码
     * @param message 异常消息
     */
    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 异常代码
     * @param message 异常消息
     * @param cause 异常原因
     */
    public BaseException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
}