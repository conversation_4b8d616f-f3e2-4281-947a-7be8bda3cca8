package com.xinhe.exception;

/**
 * 订单相关异常类
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 1.0
 */
public class OrderException extends BaseException {
    public static final int ORDER_NOT_FOUND = 3001;
    public static final int ORDER_CREATE_FAILED = 3002;
    public static final int ORDER_UPDATE_FAILED = 3003;
    public static final int ORDER_STATUS_INVALID = 3004;

    public OrderException(String message) {
        super(500, message);
    }

    public OrderException(int code, String message) {
        super(code, message);
    }

    public OrderException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 订单未找到异常
     *
     * @return OrderException
     */
    public static OrderException orderNotFound() {
        return new OrderException(ORDER_NOT_FOUND, "订单不存在");
    }

    /**
     * 订单创建失败异常
     *
     * @return OrderException
     */
    public static OrderException orderCreateFailed(String message) {
        return new OrderException(ORDER_CREATE_FAILED, "订单创建失败: " + message);
    }

    /**
     * 订单更新失败异常
     *
     * @return OrderException
     */
    public static OrderException orderUpdateFailed(String message) {
        return new OrderException(ORDER_UPDATE_FAILED, "订单更新失败: " + message);
    }

    /**
     * 订单状态无效异常
     *
     * @return OrderException
     */
    public static OrderException orderStatusInvalid() {
        return new OrderException(ORDER_STATUS_INVALID, "订单状态无效");
    }
}