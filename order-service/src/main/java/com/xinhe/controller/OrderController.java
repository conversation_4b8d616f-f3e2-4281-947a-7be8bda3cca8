package com.xinhe.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinhe.entity.Order;
import com.xinhe.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单控制器
 * 
 * <AUTHOR> Name
 */
@RestController
@RequestMapping("/api/orders")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 创建订单
     * 
     * @param order 订单信息
     * @return 创建结果
     */
    @PostMapping
    public Map<String, Object> createOrder(@RequestBody Order order,
                                          @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            order.setUserId(userId);
            boolean success = orderService.createOrder(order);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "订单创建成功");
                Map<String, Object> data = new HashMap<>();
                data.put("orderId", order.getId());
                result.put("data", data);
            } else {
                result.put("code", 500);
                result.put("message", "订单创建失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "订单创建异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取用户订单列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 订单列表
     */
    @GetMapping
    public Map<String, Object> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestHeader(value = "X-User-Id", required = false) Long userId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简化处理，实际项目中应从token中解析用户ID
            if (userId == null) {
                userId = 1L; // 默认用户ID
            }
            
            Page<Order> pageObj = new Page<>(page, size);
            IPage<Order> orderIPage = orderService.getOrdersByUserId(pageObj, userId);
            
            result.put("code", 200);
            result.put("message", "success");
            Map<String, Object> data = new HashMap<>();
            data.put("list", orderIPage.getRecords());
            data.put("total", orderIPage.getTotal());
            data.put("page", orderIPage.getCurrent());
            data.put("size", orderIPage.getSize());
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取订单列表失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情
     */
    @GetMapping("/{orderId}")
    public Map<String, Object> getOrderDetail(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Order order = orderService.getOrderDetail(orderId);
            if (order != null) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", order);
            } else {
                result.put("code", 404);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取订单详情失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 订单状态
     * @return 更新结果
     */
    @PutMapping("/{orderId}/status")
    public Map<String, Object> updateOrderStatus(@PathVariable Long orderId, @RequestParam Integer status) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = orderService.updateOrderStatus(orderId, status);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "订单状态更新成功");
            } else {
                result.put("code", 500);
                result.put("message", "订单状态更新失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "订单状态更新异常: " + e.getMessage());
        }
        
        return result;
    }
}