package com.xinhe.mq.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * MQ消息消费者基类
 * 用于统一处理各种MQ消息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-09
 */
@Slf4j
@Service
public class MQConsumerService {

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理秒杀消息
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = "seckill.queue")
    public void handleSeckillMessage(String message) {
        try {
            log.info("接收到秒杀消息: {}", message);
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            
            // 获取消息内容
            Long activityId = Long.valueOf(messageMap.get("activityId").toString());
            Long userId = Long.valueOf(messageMap.get("userId").toString());
            Long timestamp = Long.valueOf(messageMap.get("timestamp").toString());
            
            // 模拟处理秒杀订单
            log.info("开始处理秒杀订单 - 活动ID: {}, 用户ID: {}, 时间戳: {}", activityId, userId, timestamp);
            
            // 这里应该调用订单服务创建订单
            // 模拟处理过程
            Thread.sleep(100); // 模拟处理耗时
            
            log.info("秒杀订单处理完成 - 活动ID: {}, 用户ID: {}", activityId, userId);
        } catch (JsonProcessingException e) {
            log.error("解析消息失败: {}", e.getMessage(), e);
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            log.error("处理消息被中断: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理秒杀消息异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理订单消息
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = "order.queue")
    public void handleOrderMessage(String message) {
        try {
            log.info("接收到订单消息: {}", message);
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            
            // 获取消息内容
            Long orderId = Long.valueOf(messageMap.get("orderId").toString());
            
            // 模拟处理订单
            log.info("开始处理订单: {}", orderId);
            
            // 模拟处理过程
            Thread.sleep(100);
            
            log.info("订单处理完成: {}", orderId);
        } catch (JsonProcessingException e) {
            log.error("解析订单消息失败: {}", e.getMessage(), e);
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            log.error("处理订单消息被中断: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理订单消息异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理支付消息
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = "payment.queue")
    public void handlePaymentMessage(String message) {
        try {
            log.info("接收到支付消息: {}", message);
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            
            // 获取消息内容
            Long paymentId = Long.valueOf(messageMap.get("paymentId").toString());
            
            // 模拟处理支付
            log.info("开始处理支付: {}", paymentId);
            
            // 模拟处理过程
            Thread.sleep(100);
            
            log.info("支付处理完成: {}", paymentId);
        } catch (JsonProcessingException e) {
            log.error("解析支付消息失败: {}", e.getMessage(), e);
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            log.error("处理支付消息被中断: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理支付消息异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理通知消息
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = "notification.queue")
    public void handleNotificationMessage(String message) {
        try {
            log.info("接收到通知消息: {}", message);
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            
            // 获取消息内容
            String notificationType = (String) messageMap.get("type");
            String content = (String) messageMap.get("content");
            
            // 模拟发送通知
            log.info("开始发送{}通知: {}", notificationType, content);
            
            // 模拟处理过程
            Thread.sleep(100);
            
            log.info("{}通知发送完成", notificationType);
        } catch (JsonProcessingException e) {
            log.error("解析通知消息失败: {}", e.getMessage(), e);
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            log.error("处理通知消息被中断: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理通知消息异常: {}", e.getMessage(), e);
        }
    }
}