package com.xinhe.mq.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * MQ消息生产者服务
 * 用于统一发送各种MQ消息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-09
 */
@Slf4j
@Service
public class MQProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 发送秒杀消息
     *
     * @param message 消息内容
     * @param routingKey 路由键
     */
    public void sendSeckillMessage(Object message, String routingKey) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            rabbitTemplate.convertAndSend("seckill.exchange", routingKey, jsonMessage);
            log.info("发送秒杀消息成功: {}", jsonMessage);
        } catch (JsonProcessingException e) {
            log.error("序列化秒杀消息失败: {}", e.getMessage(), e);
        } catch (AmqpException e) {
            log.error("发送秒杀消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送订单消息
     *
     * @param message 消息内容
     * @param routingKey 路由键
     */
    public void sendOrderMessage(Object message, String routingKey) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            rabbitTemplate.convertAndSend("order.exchange", routingKey, jsonMessage);
            log.info("发送订单消息成功: {}", jsonMessage);
        } catch (JsonProcessingException e) {
            log.error("序列化订单消息失败: {}", e.getMessage(), e);
        } catch (AmqpException e) {
            log.error("发送订单消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送支付消息
     *
     * @param message 消息内容
     * @param routingKey 路由键
     */
    public void sendPaymentMessage(Object message, String routingKey) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            rabbitTemplate.convertAndSend("payment.exchange", routingKey, jsonMessage);
            log.info("发送支付消息成功: {}", jsonMessage);
        } catch (JsonProcessingException e) {
            log.error("序列化支付消息失败: {}", e.getMessage(), e);
        } catch (AmqpException e) {
            log.error("发送支付消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送通知消息
     *
     * @param message 消息内容
     * @param routingKey 路由键
     */
    public void sendNotificationMessage(Object message, String routingKey) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            rabbitTemplate.convertAndSend("notification.exchange", routingKey, jsonMessage);
            log.info("发送通知消息成功: {}", jsonMessage);
        } catch (JsonProcessingException e) {
            log.error("序列化通知消息失败: {}", e.getMessage(), e);
        } catch (AmqpException e) {
            log.error("发送通知消息失败: {}", e.getMessage(), e);
        }
    }
}