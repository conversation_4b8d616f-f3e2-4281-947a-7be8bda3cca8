package com.xinhe.constants;

/**
 * 公共常量类
 * 定义系统中使用的公共常量
 * 
 * <AUTHOR>
 * @since 1.0
 */
public class CommonConstants {
    
    /**
     * Redis键前缀
     */
    public static final String REDIS_KEY_PREFIX = "seckill:";
    public static final String STOCK_KEY_PREFIX = REDIS_KEY_PREFIX + "stock:";
    public static final String USER_PARTICIPATION_KEY_PREFIX = REDIS_KEY_PREFIX + "user:";
    public static final String ACTIVITY_STATUS_KEY_PREFIX = REDIS_KEY_PREFIX + "status:";
    
    /**
     * 活动状态常量
     */
    public static final int ACTIVITY_STATUS_NOT_STARTED = 0;
    public static final int ACTIVITY_STATUS_RUNNING = 1;
    public static final int ACTIVITY_STATUS_ENDED = 2;
    
    /**
     * 用户状态常量
     */
    public static final int USER_STATUS_DISABLED = 0;
    public static final int USER_STATUS_ENABLED = 1;
    
    /**
     * 订单状态常量
     */
    public static final int ORDER_STATUS_PENDING = 0;
    public static final int ORDER_STATUS_PAID = 1;
    public static final int ORDER_STATUS_CANCELLED = 2;
    public static final int ORDER_STATUS_COMPLETED = 3;
    
    /**
     * 支付状态常量
     */
    public static final int PAYMENT_STATUS_PENDING = 0;
    public static final int PAYMENT_STATUS_SUCCESS = 1;
    public static final int PAYMENT_STATUS_FAILED = 2;
    
    /**
     * 响应状态码
     */
    public static final int SUCCESS_CODE = 200;
    public static final int ERROR_CODE = 500;
    public static final int UNAUTHORIZED_CODE = 401;
    public static final int FORBIDDEN_CODE = 403;
    public static final int NOT_FOUND_CODE = 404;
    
    /**
     * 响应消息
     */
    public static final String SUCCESS_MESSAGE = "操作成功";
    public static final String ERROR_MESSAGE = "操作失败";
    public static final String UNAUTHORIZED_MESSAGE = "未授权访问";
    public static final String FORBIDDEN_MESSAGE = "禁止访问";
    public static final String NOT_FOUND_MESSAGE = "资源不存在";
    
    /**
     * 时间相关常量
     */
    public static final int DEFAULT_CACHE_TIMEOUT_SECONDS = 60;
    public static final int USER_PARTICIPATION_TIMEOUT_MINUTES = 60;
    public static final int ACTIVITY_STATUS_TIMEOUT_SECONDS = 60;
    
    /**
     * 线程池相关常量
     */
    public static final int DEFAULT_CORE_POOL_SIZE = 5;
    public static final int DEFAULT_MAX_POOL_SIZE = 20;
    public static final int DEFAULT_QUEUE_CAPACITY = 100;
    public static final int DEFAULT_KEEP_ALIVE_SECONDS = 60;
    public static final int DEFAULT_AWAIT_TERMINATION_SECONDS = 60;
    
    /**
     * JWT相关常量
     */
    public static final String JWT_TOKEN_PREFIX = "Bearer ";
    public static final String JWT_HEADER_NAME = "Authorization";
    public static final long JWT_TOKEN_VALIDITY_SECONDS = 24 * 60 * 60; // 24小时
    
    /**
     * 私有构造函数，防止实例化
     */
    private CommonConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
