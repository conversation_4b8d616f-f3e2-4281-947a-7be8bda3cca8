package com.xinhe.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 统一线程池配置类
 * 用于处理高并发场景下的异步任务执行
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-09
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 订单处理线程池
     * 用于处理高并发的订单创建请求
     *
     * @return Executor 线程池执行器
     */
    @Bean("orderTaskExecutor")
    public Executor orderTaskExecutor() {
        return createThreadPoolExecutor("order-executor-", 5, 20, 100);
    }

    /**
     * 支付处理线程池
     * 用于处理支付相关的异步任务
     *
     * @return Executor 线程池执行器
     */
    @Bean("paymentTaskExecutor")
    public Executor paymentTaskExecutor() {
        return createThreadPoolExecutor("payment-executor-", 5, 20, 100);
    }

    /**
     * 通知处理线程池
     * 用于处理支付结果通知等异步任务
     *
     * @return Executor 线程池执行器
     */
    @Bean("notificationTaskExecutor")
    public Executor notificationTaskExecutor() {
        return createThreadPoolExecutor("notification-executor-", 3, 10, 50);
    }

    /**
     * 秒杀处理线程池
     * 用于处理秒杀相关的异步任务
     *
     * @return Executor 线程池执行器
     */
    @Bean("seckillTaskExecutor")
    public Executor seckillTaskExecutor() {
        return createThreadPoolExecutor("seckill-executor-", 10, 50, 200);
    }

    /**
     * 创建线程池执行器的通用方法
     *
     * @param threadNamePrefix 线程名前缀
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param queueCapacity 队列容量
     * @return Executor 线程池执行器
     */
    private Executor createThreadPoolExecutor(String threadNamePrefix, 
                                            int corePoolSize, 
                                            int maxPoolSize, 
                                            int queueCapacity) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        // 队列容量
        executor.setQueueCapacity(queueCapacity);
        // 线程前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}
