# 公共配置文件
# 各个服务可以通过 spring.profiles.include: common 来引入此配置

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************
    username: root
    password: root
    
  # Redis配置
  redis:
    host: ***************
    port: 6379
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
        
  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
      config:
        import-check:
          enabled: false
          
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
  # 跨域配置
  mvc:
    cors:
      allowed-origin-patterns: "*"
      allowed-methods: "*"
      allowed-headers: "*"
      allow-credentials: true

# Seata分布式事务配置
seata:
  enabled: true
  tx-service-group: default_tx_group
  enable-auto-data-source-proxy: true
  service:
    vgroup-mapping:
      default_tx_group: default
    grouplist:
      default: localhost:8091
  config:
    type: file

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.xinhe: DEBUG
    org.springframework.cloud: INFO
