package com.xinhe.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 秒杀结果类
 * 统一的秒杀结果数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@NoArgsConstructor
public class SeckillResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 订单ID（如果成功创建订单）
     */
    private Long orderId;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param message 结果消息
     */
    public SeckillResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.createTime = new Date();
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param orderId 订单ID
     * @param message 结果消息
     */
    public SeckillResult(boolean success, Long orderId, String message) {
        this.success = success;
        this.orderId = orderId;
        this.message = message;
        this.createTime = new Date();
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param message 结果消息
     * @param createTime 创建时间
     */
    public SeckillResult(boolean success, String message, Date createTime) {
        this.success = success;
        this.message = message;
        this.createTime = createTime;
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param orderId 订单ID
     * @param message 结果消息
     * @param createTime 创建时间
     */
    public SeckillResult(boolean success, Long orderId, String message, Date createTime) {
        this.success = success;
        this.orderId = orderId;
        this.message = message;
        this.createTime = createTime;
    }
}
